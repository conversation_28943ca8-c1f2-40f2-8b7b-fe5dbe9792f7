const {
    doMakeApiQuery,
} = require("../../handleSparql/services/common/sparql-common");
const exportConfig = require("../../../config/config.export");
const { getApi } = require("../../handleAuth/apiObj");
const {
    ERROR_API_NOT_EXIST,
    ERROR_API_METHOD,
    ERROR_API_VER_NOT_EXIST,
} = require("../../../config/config");
const timerHelper = require("./timerHelper");

const dbHelper = {};

dbHelper.databaseExport = async (file, authToken) => {
    try {
        const { pattern } = file || {};
        //
        const startTime = new Date();
        timerHelper.timeStart("databaseExport", startTime);
        //
        let patternList = Array.isArray(pattern) ? pattern : [pattern];
        if (!(patternList[0] in exportConfig.pattern)) {
            return ERROR_API_METHOD;
        }
        const { apiMethod, apiVer } = exportConfig.pattern[patternList[0]];

        if (!apiMethod) {
            return ERROR_API_METHOD;
        }
        const apiDef = getApi("read", apiMethod);
        if (!apiDef) {
            return ERROR_API_NOT_EXIST;
        }
        if (!apiDef.hasOwnProperty(apiVer)) {
            return ERROR_API_VER_NOT_EXIST;
        }
        const apiVerObj = apiDef[apiVer];
        // console.log("apiVerObj", apiVerObj);

        return new Promise(async (resolve, reject) => {
            await doMakeApiQuery(apiVerObj.query, -1, 0)
                .then(({ head, data }) => {
                    timerHelper.timeStampEnd("databaseExport", startTime);
                    resolve({ head, data });
                })
                .catch((err) => {
                    timerHelper.timeStampEnd("databaseExport", startTime);
                    console.log("catch databaseExport err", err);
                    reject(err);
                });
        });
    } catch (err) {
        console.log("databaseExport err", err);
        return { error: err.message };
    }
};

module.exports = dbHelper;
