const { baseGraph } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const moveSparql = (oldGraph, newGraph) => {
    return `
        DELETE {
            GRAPH ${oldGraph} {
                ?s ?p ?o .
            }
        }
        INSERT {
            GRAPH ${newGraph} {
                ?s ?p ?o .
            }
        }
        WHERE {
            GRAPH ${oldGraph} {
                {
                    ?s ?p ?o .
                }
            }
        }
        `;
};

const moveInstances = (entrySrc, entryDst) => {
    const _queryStr = moveSparql(
        baseGraph(entrySrc.value),
        baseGraph(entryDst.value),
    );
    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

exports.moveInstances20 = (entrySrc, entryDst, callback) => {
    Promise.all([moveInstances(entrySrc, entryDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
