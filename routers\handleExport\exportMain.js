//
const fs = require("fs");
const path = require("path");

//
const { getApi } = require("../handleAuth/apiObj");
//
const exportConfig = require("../../config/config.export");
//
const firebaseHelper = require("./helpers/firebaseHelper");
const fileHelper = require("./helpers/fileHelper");
const mailHelper = require("./helpers/mailHelper");
const dbHelper = require("./helpers/dbHelper");
const errorHelper = require("./helpers/errorHelper");
const commonHelper = require("./helpers/commonHelper");

//
const { getDestination, getFileUrlPath } = fileHelper;

// 依據 extension 轉檔
const fileConvert = async (data, extension, patternStr) => {
    if (extension === exportConfig.EXTENSION.xlsx) {
        const dataConvert = await fileHelper.tsvToExcelBuffer(data, patternStr);
        if (dataConvert) {
            return dataConvert;
        }
    }
    return null;
};

// email 給使用者
const emailToUser = (user, fileUrlPath, status) => {
    if (status && status in mailHelper.mailMessage.user) {
        const sendMail = mailHelper.mailMessage.user[status](user, fileUrlPath);
        mailHelper.sendToUser(sendMail);
    }
};

// email 給管理者
const emailToManager = (user, fileUrlPath, status) => {
    const managerInfo = getApi("mail-config", "manager");

    if (managerInfo && status && status in mailHelper.mailMessage.manager) {
        const sendMail = mailHelper.mailMessage.manager[status](
            user,
            fileUrlPath,
            managerInfo
        );
        mailHelper.sendToUser(sendMail);
    }
};

// 執行 database 匯出流程
const exportMain = async ({
    user,
    file,
    authToken,
    fileName,
    zipFileName,
    fileKey,
}) => {
    const { pattern } = file || {};
    const patternStr = commonHelper.safeGetFirstEle(pattern);
    // 使用者提取的檔案路徑
    const fileUrlPath = getFileUrlPath(zipFileName, patternStr);
    // 先取得 realtime database 設定檔
    const rtDbPath = exportConfig.firebaseRtDb.readWritePath;

    try {
        // 1.database 匯出
        const dbExport = await dbHelper.databaseExport(file, authToken);

        if (dbExport instanceof Error || (dbExport && dbExport.error)) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.DB_EXPORT_ERROR.name
            );
        }

        // 2.轉檔
        const fileConverted = await fileConvert(
            dbExport,
            exportConfig.EXTENSION.xlsx,
            patternStr
        );
        if (!fileConverted) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.FILE_CONVERT_ERROR.name
            );
        }
        // 儲存檔案路徑
        const targetPath = getDestination(patternStr);

        // 為該路徑建立資料夾
        fs.mkdirSync(targetPath, { recursive: true }, () => {});
        const filePath = path.join(targetPath, zipFileName);
        // 3.儲存檔案
        const storeFile = await fileHelper.writeFile(
            fileName,
            fileConverted,
            filePath
        );
        // 4.email 通知 及 更新 firebase realtime db
        if (!(storeFile instanceof Error) && storeFile !== null) {
            if (fileUrlPath) {
                let rtDbDataObj, rtUpdateRes;

                // 先取得 realtime database 設定檔
                rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
                // console.log("rtDbDataObj", rtDbDataObj);

                if (rtDbDataObj instanceof Error) {
                    return errorHelper.sendErrorCode(
                        errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                    );
                }
                // firebase 更新
                if (rtDbDataObj) {
                    const updateVal =
                        firebaseHelper.getRtDbUpdate4DownloadReady({
                            data: rtDbDataObj,
                            user,
                            fileKey,
                            downloadUrl: fileUrlPath,
                        });
                    // 更新 realtime database 的資訊
                    rtUpdateRes = await firebaseHelper.rtDbUpdate(
                        rtDbPath,
                        updateVal
                    );
                }
                if (rtUpdateRes instanceof Error) {
                    return errorHelper.sendErrorCode(
                        errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                    );
                }
                // 通知使用者
                emailToUser(
                    user,
                    fileUrlPath,
                    mailHelper.mailMessage.status.success
                );
                // 通知管理者
                emailToManager(
                    user,
                    fileUrlPath,
                    mailHelper.mailMessage.status.success
                );
            }
        } else {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
            );
        }
        // firebase 更新
    } catch (err) {
        console.log("exportMain error", err.message);
        let errObj = errorHelper.ERROR_CODE.DEFAULT;
        if (err.message in errorHelper.ERROR_CODE) {
            errObj = errorHelper.ERROR_CODE[err.message];
        }
        let rtDbDataObj, rtUpdateRes;
        try {
            // 先取得 realtime database 設定檔
            rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
            // console.log("rtDbDataObj", rtDbDataObj);

            if (rtDbDataObj instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                );
            }
            // firebase 更新
            if (rtDbDataObj) {
                const updateVal = firebaseHelper.getRtDbUpdate4DownloadFail({
                    data: rtDbDataObj,
                    fileKey,
                    errorMessage: errObj.message,
                });
                // 更新 realtime database 的資訊
                rtUpdateRes = await firebaseHelper.rtDbUpdate(
                    rtDbPath,
                    updateVal
                );
            }
            if (rtUpdateRes instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                );
            }
        } catch (err) {
            console.log("err in exportMain", err.message);
        } finally {
            // 使用者提取的檔案路徑
            const fileUrlPath = getFileUrlPath(fileName, patternStr);
            // email to user
            emailToUser(
                user,
                fileUrlPath,
                mailHelper.mailMessage.status.failure
            );
            // 通知管理者
            emailToManager(
                user,
                fileUrlPath,
                mailHelper.mailMessage.status.failure
            );
        }
    }
};

module.exports = exportMain;
