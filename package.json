{"name": "hkbdb-api", "version": "2.0.0", "description": "", "main": "server.js", "scripts": {"test": "test", "start": "node server.js", "production": "cross-env NODE_ENV=production nodemon server.js", "develop": "cross-env NODE_ENV=development nodemon server.js"}, "author": "", "license": "MIT", "dependencies": {"async": "^2.6.0", "axios": "^1.8.2", "base64url": "^3.0.1", "body-parser": "^1.18.2", "commander": "^2.15.1", "cross-env": "^7.0.3", "dotenv": "^8.2.0", "express": "^4.16.3", "firebase": "^7.14.2", "firebase-admin": "^8.11.0", "http-errors": "^1.8.0", "jsonfile": "^4.0.0", "jszip": "^3.7.1", "lodash": "^4.17.10", "md5": "^2.3.0", "mongodb": "^3.1.1", "morgan": "^1.10.0", "multer": "^1.4.3", "node-schedule": "^1.3.2", "node-xlsx": "^0.17.2", "nodemailer": "^6.7.1", "rdf-parse": "^1.7.0", "redis": "^4.0.4", "showdown": "^2.1.0", "sparql-http-client": "^2.4.2", "stardog": "^3.2.0", "streamify-string": "^1.0.1", "zlib": "^1.0.5"}, "devDependencies": {"babel-eslint": "^10.0.3", "eslint": "5.16.0", "eslint-config-prettier": "^2.9.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.13.0", "eslint-plugin-node": "^9.2.0", "eslint-plugin-prettier": "^2.6.1", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-react": "^7.10.0", "eslint-plugin-standard": "^4.1.0", "gulp": "^4.0.2", "gulp-eslint": "^4.0.2", "gulp-nodemon": "^2.5.0", "nodemon": "^2.0.7", "prettier": "^2.3.2"}}