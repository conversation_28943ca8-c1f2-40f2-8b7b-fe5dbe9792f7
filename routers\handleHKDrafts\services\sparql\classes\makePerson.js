const { doQueryNxtMaxId } = require("../../common/sparql-common");

// =======================================
// Person
// =======================================
const _bindId = "personId";
const _className = "Person";
const _classPrefix = "PER";

exports.personBindId = _bindId;
exports.personClassName = _className;
exports.personClassPrefix = _classPrefix;

exports.createPerson = () => {
    const resObj = { insert: "", remove: "", where: "" };
    resObj.where += doQueryNxtMaxId(_className, _classPrefix, _bindId);
    return resObj;
};
