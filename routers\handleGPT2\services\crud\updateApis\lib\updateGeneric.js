const { namespace } = require("../../../sparql/rdf");
const { updateObj } = require("../../../sparql/properties/generic");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const generic = async (entrySrc, entryDst) => {
    const { graph } = entrySrc;
    const { remove, insert, where } = updateObj(entrySrc, entryDst);

    let qryDelete = "";
    if (remove && remove !== "") {
        qryDelete = `DELETE {
    GRAPH <${namespace.graph}${graph}> {
        ${remove}
    }
}`;
    }

    let qryInsert = "";
    if (insert && insert !== "") {
        qryInsert = `INSERT {
    GRAPH <${namespace.graph}${graph}> {
        ${insert}
    }
}`;
    }

    const _queryStr = `${qryDelete}${qryInsert} WHERE { ${where} }`;

    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
    // return new Promise((resolve) => resolve({ status: 200 }))
};

exports.generic20 = async (entrySrc, entryDst, callback) => {
    await Promise.all([generic(entrySrc, entryDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
