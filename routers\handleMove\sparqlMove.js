const { decodePersonId } = require("../handleSparql/services/common/common");

const { verifyToken } = require("../handleAuth/auth");
const updateApiList = require("../handleSparql/services/crud/updateApis");
const {
    ERROR_NO_PARAM_VER,
    ERROR_API_NOT_EXIST,
    ERROR_API_METHOD,
    ERROR_NO_CHANGE,
    ERROR_LACK_OF_PARAMS,
    ERROR_API_VER_NOT_EXIST,
    ERROR_NO_PAYLOAD,
} = require("../../config/config");
const { isObjectEqual } = require("../handleSparql/services/common/common");

function sparqlMove(req, res) {
    // auth
    const authToken = req.headers.authorization;
    //
    let { entrySrc, entryDst } = req.body;
    entrySrc = decodePersonId(entrySrc);
    entryDst = decodePersonId(entryDst);
    //
    // The update data is existing or not.
    if (!entrySrc || !entryDst) {
        return res.status(400).send(ERROR_NO_PAYLOAD);
    }

    if (isObjectEqual(entrySrc, entryDst)) {
        return res.status(400).send(ERROR_NO_CHANGE);
    }

    // The API has to have the version.
    const { ver } = req.params;
    if (!ver) {
        return res.status(400).send(ERROR_NO_PARAM_VER);
    }

    // The API method is not correct.
    const apiMethod = req.params[0];
    if (!apiMethod) {
        return res.status(400).send(ERROR_API_METHOD);
    }

    // The API method doesn't exist.
    const apiDef = updateApiList[apiMethod];
    if (!apiDef) {
        return res.status(400).send(ERROR_API_NOT_EXIST);
    }

    // parameters required
    const lackParms = apiDef.required.find((r) => {
        return !entrySrc.hasOwnProperty(r) || !entryDst.hasOwnProperty(r);
    });
    if (lackParms) {
        return res.status(400).send(ERROR_LACK_OF_PARAMS);
    }

    if (!apiDef.hasOwnProperty(ver)) {
        return res.status(400).send(ERROR_API_VER_NOT_EXIST);
    }
    const apiVer = apiDef[ver];

    verifyToken(authToken)
        .then(() => {
            apiVer(entrySrc, entryDst, (response, error) => {
                if (error) {
                    return res.status(400).send({ error });
                }
                const foundError = response.find((r) => {
                    return r.status !== 200;
                });
                if (foundError) {
                    return res
                        .status(foundError.status)
                        .send({ error: foundError.statusText });
                }
                return res.status(200).send({ data: "OK" });
            });
        })
        .catch((error) => {
            // console.log(error);
            res.status(400).send({ error });
        });
}

module.exports = sparqlMove;
