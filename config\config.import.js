const storeDriver = require("../routers/handleSparql/services/sparql/storeDriver");

const GRAPH = {
    HKLIT: "hklit",
    AUDA_HKLIT: "auda_hklit",
    // AUDA_HKLIT: "test", // fixme:測試用
};

// import file extension
const EXTENSION = {
    xlsx: "xlsx",
};

const getYYYYMMDD = () => {
    const twoDigits = (num) => (Number(num) / 10 < 1 ? `0${num}` : num);
    const date = new Date();
    return `${date.getFullYear()}-${twoDigits(date.getMonth() + 1)}-${twoDigits(
        date.getDate()
    )}-${twoDigits(date.getHours())}${twoDigits(date.getMinutes())}`;
};

const DB_BACKUP_FILE_TYPE_EXT = {
    trig: "trig",
    json: "json",
};

// 提供給 stardog sdk 使用
const DB_BACKUP_FILE_TYPE_MINE_TYPE = {
    trig: "application/trig",
    json: "application/ld+json",
};

let FIRE_BASE_RTDB_ROOT = "/database/local";
if (process.env.NODE_ENV === "production") {
    FIRE_BASE_RTDB_ROOT = "/database/production";
} else if (process.env.NODE_ENV === "development") {
    FIRE_BASE_RTDB_ROOT = "/database/development";
}

const getBackupFileName = (ext) => {
    return ext
        ? `db_${storeDriver.getDbName()}-graph_${
            GRAPH.AUDA_HKLIT
        }-${getYYYYMMDD()}.${ext}`
        : `db_${storeDriver.getDbName()}-graph_${
            GRAPH.AUDA_HKLIT
        }-${getYYYYMMDD()}.trig`;
};

const getBackupMineType = (ext) => {
    return (
        (ext &&
            ext in DB_BACKUP_FILE_TYPE_MINE_TYPE &&
            DB_BACKUP_FILE_TYPE_MINE_TYPE[ext]) ||
        DB_BACKUP_FILE_TYPE_MINE_TYPE.trig
    );
};

// 每個 pattern 設定:
// - excel 要有哪些欄位
const importConfig = {
    GRAPH: GRAPH,
    EXTENSION: EXTENSION,
    dbBackupDir: "backup", // 資料庫備份存放檔案資料夾
    DB_BACKUP_FILE_TYPE_EXT: DB_BACKUP_FILE_TYPE_EXT,
    DB_BACKUP_FILE_TYPE_MINE_TYPE: DB_BACKUP_FILE_TYPE_MINE_TYPE,
    pattern: {
        auda_hklit: {
            name: "auda_hklit",
            BACKUP_GRAPH: `http://hkbdb.lib.cuhk.edu.hk/v1/${GRAPH.AUDA_HKLIT}`,
            getBackupFileName: getBackupFileName,
            getBackupMineType: getBackupMineType,
            getDatabase: storeDriver.getDbName,
            graph: GRAPH.AUDA_HKLIT,
            extension: EXTENSION.xlsx,
            // 必須要有以下欄位
            fileCols: [
                // todo: automatically add nameId if file without Name_ID
                "Name_ID",
                "Best_Known_Name",
                // todo: automatically add localNameId if file without Local_Name_ID
                "Local_Name_ID", // file 必須提供 Local_Name_ID,
                "Name_Type",
                "Name",
                "HKCAN_ID", // fixme: 需確認 有無底線
                "LCNAF",
                "VIAF",
                "Wikidata",
                "Year_of_Birth",
                "Year_of_Death",
                "Remark_Name",
                "Remark_Year",
                "Remark_Other",
            ],
            // 特殊確認的欄位
            fileColsCheck: {
                Best_Known_Name: "Best_Known_Name",
            },
        },
        // publications: {
        //     name: "publications",
        //     database: STARDOG_DB,
        //     // graph: GRAPH.AUDA_HKLIT,
        //     graph: "test", // 測試用 graph test
        //     sparql: "",
        //     extension: EXTENSION.xlsx,
        //     fileCols: [],
        //     // 特殊確認的欄位
        //     fileColsCheck: {
        //         Best_Known_Name: "Best_Known_Name",
        //     },
        // },
    },
    firebaseRtDb: {
        readWritePath: `${FIRE_BASE_RTDB_ROOT}/import`,
    },
};

module.exports = importConfig;
