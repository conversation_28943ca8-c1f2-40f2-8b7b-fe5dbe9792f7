# Handle Advanced Query Module

這個模組處理前台進階查詢功能，包括使用者查詢和公開查詢的管理。

## 功能

### 1. 取得使用者查詢 (getUserQueries)

- **路徑**: `GET /advanced-query/user-queries`
- **參數**: `uid` (query parameter) - 使用者 ID
- **功能**: 取得特定使用者的所有儲存查詢
- **回傳**: 查詢陣列，每個查詢包含 id 和其他資料

### 2. 取得公開查詢 (getPublicQueries)

- **路徑**: `GET /advanced-query/public-queries`
- **參數**: 無
- **功能**: 取得所有公開的儲存查詢
- **回傳**: 公開查詢陣列，每個查詢包含 id 和其他資料

## 使用方式

### 前端呼叫範例

```javascript
// 取得使用者查詢
const getUserQueries = async (uid, authToken) => {
    try {
        const response = await fetch(`/advanced-query/user-queries?uid=${uid}`, {
            method: 'GET',
            headers: {
                'Authorization': authToken,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        return result.data; // 回傳查詢陣列
    } catch (error) {
        console.error('取得使用者查詢失敗:', error);
        return [];
    }
};

// 取得公開查詢
const getPublicQueries = async (authToken) => {
    try {
        const response = await fetch('/advanced-query/public-queries', {
            method: 'GET',
            headers: {
                'Authorization': authToken,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        return result.data; // 回傳查詢陣列
    } catch (error) {
        console.error('取得公開查詢失敗:', error);
        return [];
    }
};

// 使用範例
const uid = 'user123';
const authToken = 'Bearer your-auth-token';

const userQueries = await getUserQueries(uid, authToken);
const publicQueries = await getPublicQueries(authToken);
```

## 檔案結構

```
routers/handleAdvancedQuery/
├── index.js                    # 路由定義 (備用，主要路由在 routerManager.js)
├── advancedQueryController.js  # 控制器邏輯 (包含身份驗證和 cache 支援)
├── services/
│   └── queryService.js        # Firebase 查詢服務
└── README.md                  # 說明文件
```

## 路由註冊方式

與其他有 cache 的路由一致，直接在 `routerManager.js` 中定義：

```javascript
// advanced query
app.get("/advanced-query/user-queries", redisGetCache, advancedQueryController.getUserQueries, redisSetCache);
app.get("/advanced-query/public-queries", redisGetCache, advancedQueryController.getPublicQueries, redisSetCache);
```

## 注意事項

1. 所有 API 都需要驗證 (Authorization header)
2. Firebase Firestore 集合名稱: `storedQuery`
3. 查詢條件:
   - 使用者查詢: `author.uid == uid`
   - 公開查詢: `private == false`
4. 回傳的資料會自動加入文件 ID (`data.id = snapshot.id`)
5. **Cache 機制**:
   - 使用 Redis cache 來提升效能
   - 只在 production 環境啟用
   - Cache key 基於完整的 API URL
   - 適合查詢頻繁但更新較少的資料

## 錯誤處理

- 缺少必要參數會回傳 400 Bad Request
- Firebase 查詢錯誤會被捕捉並回傳適當的錯誤訊息
- 所有錯誤都會記錄在 console 中
