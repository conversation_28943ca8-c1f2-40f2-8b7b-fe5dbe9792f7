const config = {};

// project prefix
config.PRJ_PREFIX = "hkbdb_";

// System definition
config.ALLOWED_DOMAIN = ".daoyidh.com";
config.ALLOWED_ORIGINS = [
    "https://hkbdb.lib.cuhk.edu.hk",
    "http://localhost:3000",
    "http://localhost:3100",
    "http://localhost:3020",
    "http://localhost:4000",
];

config.REDUNDANT_PREFIX = "_";

// ERROR Codes
config.ERROR_NO_PAYLOAD = { error: "Payload is required." };
config.ERROR_NO_PARAM_VER = { error: "No ver parameter." };
config.ERROR_NO_CHANGE = { error: "Data are the same." };
config.ERROR_LACK_OF_PARAMS = { error: "Lack of parameters:" };
config.ERROR_WRONG_PARAMS = { error: "Wrong parameters:" };
config.ERROR_RESPONSE = { error: "Error response." };
config.ERROR_API_METHOD = { error: "API does not exist." };
config.ERROR_API_VER_NOT_EXIST = { error: "This API version does not exist." };
config.ERROR_API_NOT_EXIST = { error: "API or version does not exist." };
config.ERROR_GRAPH_DIFFERENT = { error: "The graphs are different." };
config.ERROR_GRAPH_REQUIRED = { error: "The graph parameter is required." };
config.ERROR_INVALID_TOKEN = { error: "Unauthorized: Invalid token" };
config.ERROR_MISSING_TEXT = { error: "Missing or invalid 'text'" };
config.ERROR_GENERATE_SPARQL = {
    error: "Error generating SPARQL query.",
};
config.HEALTH_ALIVE = "alive";
config.RESPONSE_OK = 200;
config.RESPONSE_BAD_REQUEST = 400;
config.RESPONSE_UNAUTHORIZED = 401;

// Parameter define
config.DEFAULT_MAX_LIMIT = 10;
config.DEFAULT_OFFSET = 0;

// For feature control
config.FEATURE_STORE_LIST = { stardog: 1, fuseki: 2 };
// 1, 2
config.FEATURE_STORE = 2;

// 在 API 進行遠端 Fuseki 帳密的填入
// config.SERVICE_NMTLWORDS = "SERVICE_NMTLWORDS";

module.exports = config;
