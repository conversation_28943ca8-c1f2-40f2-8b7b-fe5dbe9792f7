const firebase = require("../../config/config.firebase");
const firestoreDb = firebase.firestore();

const apiListener = (callback) => {
    // Firebase 下載資料
    const colRef = firestoreDb.collection("api");

    colRef.onSnapshot(
        (docSnapshot) => {
            console.log(`Received doc snapshot: ${docSnapshot}`);
            colRef
                .get()
                .then((result) => {
                    const apiObj = {};
                    result.docs.forEach((x) => {
                        apiObj[x.id] = x.data();
                    });
                    callback(apiObj);
                })
                .catch((error) => {
                    console.log(error);
                    callback(null);
                });
        },
        (err) => {
            // console.log(`Encountered error: ${err}`);
            callback(null);
        }
    );
};

module.exports = apiListener;
