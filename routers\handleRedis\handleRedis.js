// redis cache
const redis = require("redis");
const md5 = require("md5");
const {
    PRJ_PREFIX,
    RESPONSE_BAD_REQUEST,
    RESPONSE_OK,
} = require("../../config/config");

// Cache
// use local redis server
const client = redis.createClient();
client.connect();

module.exports = {
    async redisGetCache(req, res, next) {
        if (process.env.NODE_ENV === "production") {
            const apiMethod = `${PRJ_PREFIX}${req.originalUrl}`;
            const value = await client.get(apiMethod);
            if (value) {
                res.status(RESPONSE_OK).send(JSON.parse(value));
            } else {
                next();
            }
        } else {
            next();
        }
    },
    async redisSetCache(req, res) {
        if (process.env.NODE_ENV === "production") {
            const apiMethod = `${PRJ_PREFIX}${req.originalUrl}`;
            if (req.daoyiStatus !== RESPONSE_BAD_REQUEST) {
                await client.set(apiMethod, JSON.stringify(req.daoyiData));
                res.status(RESPONSE_OK).send(req.daoyiData);
            } else {
                res.status(req.daoyiStatus).send({ error: req.daoyiData });
            }
        } else {
            res.status(RESPONSE_OK).send(req.daoyiData);
        }
    },
    async redisGetMassiveCache(req, res, next) {
        // production 才打開 Redis
        if (process.env.NODE_ENV === "production") {
            const payloadMd5 = md5(JSON.stringify(req.body));

            const apiMethod = `${PRJ_PREFIX}${req.originalUrl}${payloadMd5}`;
            const value = await client.get(apiMethod);
            if (value) {
                res.status(RESPONSE_OK).send(JSON.parse(value));
            } else {
                next();
            }
        } else {
            next();
        }
    },
    async redisSetMassiveCache(req, res) {
        // production 才打開 Redis
        if (process.env.NODE_ENV === "production") {
            const payloadMd5 = md5(JSON.stringify(req.body));

            const apiMethod = `${PRJ_PREFIX}${req.originalUrl}${payloadMd5}`;
            if (req.daoyiStatus !== RESPONSE_BAD_REQUEST) {
                await client.set(apiMethod, JSON.stringify(req.daoyiData));
                res.status(RESPONSE_OK).send(req.daoyiData);
            } else {
                res.status(req.daoyiStatus).send({ error: req.daoyiData });
            }
        } else {
            res.status(RESPONSE_OK).send(req.daoyiData);
        }
    },
    async redisFlushCache(req, res, next) {
        if (process.env.NODE_ENV !== "production") {
            return next();
        }
        const keys = await client.keys(`${PRJ_PREFIX}*`);
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            await client.del(key);
        }
        next();
    },
    async redisDumpCache(req, res, next) {
        if (process.env.NODE_ENV !== "production") {
            return next();
        }
        const keys = await client.keys(`${PRJ_PREFIX}*`);
        res.status(RESPONSE_OK).send(keys);
    },
};
