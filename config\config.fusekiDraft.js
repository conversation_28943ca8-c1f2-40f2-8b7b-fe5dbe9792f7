const configFuseki = {};
configFuseki.port = 5822;
if (process.env.NODE_ENV === "production") {
    const {
        db,
        url,
        user,
        password,
        draftDB,
    } = require("/opt/private-config/hkbdb-fusekiDraft.json");
    configFuseki.db = db;
    configFuseki.url = url;
    configFuseki.user = user;
    configFuseki.password = password;
    configFuseki.draftDB = draftDB;
} else {
    configFuseki.db = "hkbdb2_draft";
    configFuseki.url = `http://localhost:${configFuseki.port}`;
    configFuseki.user = "admin";
    configFuseki.password = "admin";
    configFuseki.draftDB = "hkbdb2_draft";
}

console.log("[ENV] FUSEKI_DRAFT_DB", configFuseki.db);
console.log("[ENV] FUSEKI_DRAFT_URL", configFuseki.url);

module.exports = configFuseki;
