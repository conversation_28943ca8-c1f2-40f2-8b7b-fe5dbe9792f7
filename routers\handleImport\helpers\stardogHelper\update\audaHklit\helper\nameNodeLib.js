const {
    doMakeApiQuery,
} = require("../../../../../../handleSparql/services/common/sparql-common");
const { getApi } = require("../../../../../../handleAuth/apiObj");
const {
    replaceQueryParams,
} = require("../../../../../../handleSparql/services/common/common");

const NAME_TYPE = {
    nnBestKnownName: {
        key: "nnBestKnownName",
        prop: "nnBestKnownName",
        serialStart: "00001",
        colVal: "best known name",
    },
    originalName: {
        key: "originalName",
        prop: "originalName",
        serialStart: "00002",
        colVal: "original name",
    },
    penName: {
        key: "penName",
        prop: "penName",
        serialStart: "01001",
        colVal: "pen name",
    },
    zi: {
        key: "zi",
        prop: "zi",
        serialStart: "02001",
        colVal: "zi",
    },
    hao: {
        key: "hao",
        prop: "hao",
        serialStart: "03001",
        colVal: "hao",
    },
    joinPenName: {
        key: "joinPenName",
        prop: "joinPenName",
        serialStart: "04001",
        colVal: "join pen name",
    },
    name: {
        key: "name",
        prop: "name",
        serialStart: "05001",
        colVal: "name",
    },
};

const getNameTypeKeyByColVal = (nameTypeColVal) => {
    const find = Object.values(NAME_TYPE).find(
        (obj) => obj.colVal === nameTypeColVal
    );
    if (!find) return null;
    return find.key;
};

// 取得下一個 nameId dependent by person or organization
const getNextNameId = async (classType) => {
    try {
        if (!(classType && ["Person", "Organization"].includes(classType)))
            return null;
        const apiMethod = "get/nextNameId";
        const ver = "2.0";
        const apiDef = getApi("read", apiMethod);
        // console.log("apiDef", apiDef);
        if (!apiDef) {
            return Promise.resolve(null);
        }

        if (!apiDef.hasOwnProperty(ver)) {
            return Promise.resolve(null);
        }

        const apiVer = apiDef[ver];
        const reqQuery = {
            class: classType,
        };
        const language = "en";
        // The query string.
        let apiQuery = replaceQueryParams(
            apiVer.query,
            reqQuery,
            apiVer.key,
            language
        );

        // api 自己 query, 不需要 token
        return doMakeApiQuery(apiQuery, -1, 0)
            .then((res) => {
                const { data } = res;
                // console.log("data", data);
                if (data && Array.isArray(data) && data.length > 0) {
                    return data[0].maxNum;
                } else {
                    return null;
                }
            })
            .catch((err) => {
                console.log(err);
                return null;
            });
    } catch (err) {
        return null;
    }
};

// 依據 name type 取得 localNameId
const getLocalNameId = (nameId, nameType) => {
    if (nameId && nameType && nameType in NAME_TYPE) {
        return `${nameId}${NAME_TYPE[nameType].serialStart}`;
    }
};

// localNameId: nameId(7碼) + nameTypeId (5碼)
const NAME_ID_LEN = 7;
const NAME_TYPE_ID_LEN = 5;

// e.g. 3000155 => 3000156
// e.g. 0000155 => 0000156
const getNextNameIdStr = (nameId) => {
    const nameIdStr = String(nameId);
    const nextNameIdInt = Number.parseInt(nameIdStr) + 1;
    const zeroStr = Array(NAME_ID_LEN - String(nextNameIdInt).length)
        .fill("0")
        .join("");
    return zeroStr + `${String(nextNameIdInt)}`;
};

// e.g. 300015501001 => 300015501002
// e.g. 000015501001 => 000015501001
const getNextLocalNameId = (localNameId, nameTypeKey) => {
    if (
        [NAME_TYPE.nnBestKnownName.name, NAME_TYPE.originalName.name].includes(
            nameTypeKey
        )
    )
        return localNameId;
    const localNameIdStr = String(localNameId);

    const nameIdStr = localNameIdStr.substring(0, NAME_ID_LEN);
    const postId = localNameIdStr.substring(NAME_ID_LEN);
    const nextPostIdInt = Number.parseInt(postId) + 1;
    const zeroStr = Array(NAME_TYPE_ID_LEN - String(nextPostIdInt).length)
        .fill("0")
        .join("");
    const nexPostIdStr = zeroStr + `${nextPostIdInt}`;
    return `${nameIdStr}${nexPostIdStr}`;
};

module.exports = {
    getNextNameId,
    getNextNameIdStr,
    getNextLocalNameId,
    getNameTypeKeyByColVal,
    getLocalNameId,
};
