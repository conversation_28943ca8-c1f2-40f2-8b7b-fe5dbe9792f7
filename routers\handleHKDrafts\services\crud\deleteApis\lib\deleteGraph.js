const { doSaprqlUpdate } = require("../../../common/sparql-common");
const { baseGraph } = require("../../../sparql/rdf");
const {bs64DecodeQueryStr} = require("../../../common/common");

const deleteGraph = (graph) => {
    //
    const _queryStr = `
        DELETE {
            GRAPH ${graph} {
                ?s ?p ?o .
            }
        }
        WHERE {
            GRAPH ${graph} {
            { 
                ?s ?p ?o . 
            }
        }
        }
    `;

    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

exports.deleteGraph20 = (entry, callback) => {
    Promise.all([deleteGraph(baseGraph(entry.graph))])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
