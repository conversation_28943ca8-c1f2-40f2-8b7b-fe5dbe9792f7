// firebase
const firebase = require("../../../config/config.firebase");
const rtdb = firebase.database();

// global
let rtDbDataObj;
const firebaseHelper = {
    getRtDbDataObj: () => rtDbDataObj,
    setRtDbDataObj: (data) => {
        rtDbDataObj = data;
    },
};

firebaseHelper.rtDbGet = (path) => {
    const data = {};
    return rtdb
        .ref(path)
        .once("value")
        .then((snapshot) => {
            const snapVal = snapshot.val() || {};
            if (typeof snapVal === "object") {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
                return data;
            }
            if (typeof snapshot.val() === "string") {
                throw new Error("firebase data not valid");
            }
        })
        .catch((err) => {
            console.log("firebaseHelper.rtDbGet", err.message);
            return err;
        });
};

firebaseHelper.rtDbUpdate = (path, updateVal) => {
    if (!(path && updateVal)) return null;
    return firebase
        .database()
        .ref(path)
        .update(updateVal)
        .then((res) => {
            return res;
        })
        .catch((err) => {
            console.log("firebaseHelper.rtDbUpdate", err.message);
            return err;
        });
};

// 更新 realtime database 的資訊: files 新增 file 資訊, register 新增 file 及 user 資訊
firebaseHelper.genRtDbSnapValRegister = ({
    data = {},
    pattern,
    user,
    fileName,
    fileKey,
}) => {
    const ts = Date.now();
    const { name, email } = user || {};
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                requestTime: new Date(ts),
                fileName: fileName,
                pattern: pattern,
                error: null,
                finishTime: null,
                requestUser: name,
                requestEmail: email,
            },
        },
    };
};

firebaseHelper.getRtDbUpdate4ImportFail = ({
    data = {},
    fileKey,
    errorMessage,
}) => {
    const ts = Date.now();

    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                ...(data || {}).files[fileKey],
                error: "true",
                errorMessage: errorMessage,
                errorTime: new Date(ts),
            },
        },
    };
};

firebaseHelper.getRtDbUpdate4ImportSuccess = ({
    data = {},
    fileKey,
    backupFileName,
}) => {
    const ts = Date.now();
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                ...data.files[fileKey],
                finishTime: new Date(ts),
                backupFileName: backupFileName,
            },
        },
    };
};

module.exports = firebaseHelper;
