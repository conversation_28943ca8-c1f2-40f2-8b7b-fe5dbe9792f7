const ClassConfig = {
    Person: {
        name: "Person",
        prefix: "PER",
    },
    Organization: {
        name: "Organization",
        prefix: "OR<PERSON>",
    },
    NameNode: {
        name: "NameNode",
        prefix: "NNI",
    },
};

const DataColMap = {
    nameId: "Name_ID",
    bestKnownName: "Best_Known_Name",
    nameType: "Name_Type",
    // source: "Remark_Name",
    localNameId: "Local_Name_ID",
    hkcanId: "HKCAN ID", // 無底線
    lcnaf: "LCNAF",
    viaf: "VIAF",
    remarkOther: "Remark_Other",
    wikidata: "Wikidata",
    remarkName: "Remark_Name",
    remarkYear: "Remark_Year",
    yearOfBirth: "Year_of_Birth",
    yearOfDeath: "Year_of_Death",
};

const PersonProp = {
    label: "bestKnownName",
    bestKnownName: "bestKnownName",
    nameId: "nameId",
};

const OrganizationProp = {
    label: "bestKnownName",
    bestKnownName: "bestKnownName",
    nameId: "nameId",
};

const NameNodeProp = {
    label: "localNameId",
    nnBestKnownName: "nnBestKnownName",
    originalName: "originalName",
    penName: "penName",
    zi: "zi",
    hao: "hao",
    joinPenName: "joinPenName",
    name: "name",
    localNameId: "localNameId",
    hkcanId: "hkcanId",
    lcnaf: "lcnaf",
    viaf: "viaf",
    remarkOther: "remarkOther",
    wikidata: "wikidata",
    remarkName: "remarkName",
    remarkYear: "remarkYear",
    yearOfBirth: "yearOfBirth",
    yearOfDeath: "yearOfDeath",
};

// 是否要產生語系標籤 e.g. hkbdb:PER01 hkbdb:nnBestKnownName "xxxx"@zh
const AllowLangTagProp = ["nnBestKnownName"];

module.exports = {
    ClassConfig,
    DataColMap,
    PersonProp,
    OrganizationProp,
    NameNodeProp,
    AllowLangTagProp,
};
