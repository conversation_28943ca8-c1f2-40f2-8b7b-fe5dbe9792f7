const { baseIri } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const mergeSparql = (iriSrc, iriDst, typeSrc, typeDst) => `
DELETE {
    GRAPH ?g {
        ?s ?p ?o .
    }
}
INSERT {
    GRAPH ?g {
        ?newS ?newP ?newO .
    }
}
WHERE {
    GRAPH ?g {
        {
            BIND(${iriSrc} AS ?s) .
            ?s ?p ?o .
            BIND(${iriDst} AS ?newS) .
            BIND(?p AS ?newP) .
            BIND(?o AS ?newO2) .
        }
        UNION
        {
            BIND(${iriSrc} AS ?o) .
            ?s ?p ?o .
            BIND(?s AS ?newS) .
            BIND(?p AS ?newP) .
            BIND(${iriDst} AS ?newO2) .
        }
        BIND(IF(?newO2 = hkbdb:${typeSrc}, hkbdb:${typeDst}, ?newO2) AS ?newO) .
    }
}
`;

const mergeInstances = (entrySrc, entryDst, typeSrc, typeDst) => {
    const _queryStr = mergeSparql(
        baseIri(entrySrc.srcId),
        baseIri(entryDst.srcId),
        typeSrc,
        typeDst
    );
    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

exports.mergeInstances20 = (entrySrc, entryDst, typeSrc, typeDst, callback) => {
    Promise.all([mergeInstances(entrySrc, entryDst, typeSrc, typeDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
