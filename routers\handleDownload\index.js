const express = require("express");
const router = express.Router();
const downloadController = require("./downloadController");
const { verifyToken } = require("../handleAuth/auth");
const { UNAUTHORIZED } = require("../../commons/httpStatusCode");
const { CustomError } = require("../handleError");

const authHandler = (req, res, next) => {
    const authToken = req.headers.authorization;
    console.log("authToken", authToken);
    next();
    // return verifyToken(authToken)
    //     .then(() => {
    //         next();
    //     })
    //     .catch((error) => {
    //         console.log(error);
    //         next(new CustomError(UNAUTHORIZED));
    //     });
};

router.get(
    "/template/:pattern",
    authHandler,
    downloadController.downloadTemplate
);

module.exports = router;
