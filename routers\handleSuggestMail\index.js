const docListener = require("./docListener");
const handleSendSuggestMail = require('./handleSendSuggestMail')
const suggestMailHelper = require('./helper')
const showdown  = require('showdown')
const storeDriver = require("../handleHKDrafts/services/sparql/storeDriverDraft");
const {db} = require("../../config/config.fusekiDraft");
const storeDriverDraft = require("../handleHKDrafts/services/sparql/storeDriverDraft");
const schedule = require("node-schedule");
const handleMail = require("../handleMail");
let timeConfig = null;
let editorTimeConfig = {}
let suggesterTimeConfig = {}

// const tem = `
//     <table>
//       <tr>
//         <th>Column 1</th>
//         <th>Column 2</th>
//       </tr>
//       <tr>
//         <td>Row 1 Content</td>
//         <td>Row 1 Content</td>
//       </tr>
//     </table>`;

async function handleSuggestMail (req, res, next) {
    const INSTANCE_NUMBER = process.env.INSTANCE_NUMBER || 'default';
    if (INSTANCE_NUMBER !== '3') return;
    let job = null;

    // reset sparql
    storeDriverDraft.setSparql({});
    const pendingDataLength = await suggestMailHelper.getGraph(-1,0).then((res)=>{
        const rs = res?.results?.bindings

        const pendingData = rs.filter(item => {
            const value = item.g.value.toLowerCase();
            return !value.includes('approved') && !value.includes('rejected');
        });
        return pendingData.length
        // let pendingArr = [];
        //
        // const values = pendingData.map(item => {return `<${item.g.value}>`});

        // const createQuery = (graph) => {
        //     const queryStr = `
        //         SELECT DISTINCT *
        //         WHERE {
        //             GRAPH ${graph} {
        //               ?s ?p ?o.
        //             }
        //         }`
        //     ;
        //
        //     return storeDriver.makeQuery(queryStr,-1,0)
        //         .then((response) => {
        //             if (response.status !== 200) {
        //                 console.log(response);
        //             }
        //             return response?.results.bindings;
        //         })
        //         .catch((error) => {
        //             throw error;
        //         });
        // };
        // const executeQueriesSequentially = async () => {
        //     for (const graph of values) {
        //         try {
        //             const result = await createQuery(graph);
        //             console.log(`Query result for ${graph}:`, result);
        //         } catch (error) {
        //             console.error(`Error for ${graph}:`, error);
        //         }
        //     }
        // };
        // executeQueriesSequentially();
    });

    // let mailList = null;
    let editorMailTemplate = null;
    function formatEditorMail (data){
        return data.split(";");
    }
    // Get suggest config from firestore database
    await docListener('web','suggest-config',(data)=>{
        if(!data){
            return;
        }

        // Get content from firestore
        // const converter = new showdown.Converter();
        // html = converter.makeHtml(data.suggesterMailTemplate);
        editorMailTemplate = formatEditorMail(data.mailList).map((i)=>({
            from: "<EMAIL>",
            to: i,
            subject: "HKBDB Suggestions",
            html:`<p>您有來自HKBDB suggestions 共 ${pendingDataLength} 筆資料待審核。</p>`,
        }))
    })

    // Get time config from firestore
    await docListener('web', 'time-config',(data)=>{
        if(!data){
            return;
        }
        // 取消先前的job
        if (job) {
            job.cancel();
        }
        const {editor,suggester} = data;
        editorTimeConfig = suggestMailHelper.formatTime(editor);
        suggesterTimeConfig = suggestMailHelper.formatTime(suggester);
        timeConfig = data;

        function generateCronTime  (config, type, fct, template) {
            const {activateOn} = type
            const {weekly,daily} = config;
            if(!activateOn || !weekly || !daily || !template){
                return;
            }
            if(activateOn === 'never'){
                return;
            }
            if (activateOn !== 'never') {
                let cronTime = '';
                if (activateOn === 'weekly') {
                    cronTime = `${weekly.minute} ${weekly.hour} * * ${weekly.date}`;
                } else if (activateOn === 'daily') {
                    cronTime = `${daily.minute} ${daily.hour} */${daily.date} * *`;
                }

                async function handleSendSuggestMail (time,data) {
                    const scheduledJob = schedule.scheduleJob(time, ()=>{
                        handleMail({body:{sendMail:data}},null,(err) => {})
                    })

                    job = scheduledJob;
                    // my_job.cancel();
                    //  every 3 day
                    // '0 38 14 */3 * *'

                    //  every monday , Here the 1 refers to Monday in the week (0-6 are Sunday-Saturday).
                    // '0 38 14 * * 1'

                    //  every day at 14:38
                    // '38 14 * * *'

                }

                template.map((i)=>{
                    handleSendSuggestMail(cronTime, i)
                })
            }
        }

        // editor
        generateCronTime(editorTimeConfig, timeConfig.editor,handleSendSuggestMail,editorMailTemplate);

        // suggester
        // generateCronTime(suggesterTimeConfig, timeConfig.suggester,handleSendSuggestMail,editorMailTemplate);
    })
}
module.exports = handleSuggestMail;
