const queryService = require("./services/queryService");
const { verifyToken } = require("../handleAuth/auth");
const { RESPONSE_BAD_REQUEST, RESPONSE_OK } = require("../../config/config");

const advancedQueryController = {
    /**
     * 取得使用者的查詢
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next function
     */
    getUserQueries: async (req, res, next) => {
        const authToken = req.headers.authorization;

        await verifyToken(authToken)
            .then(async () => {
                try {
                    const { uid } = req.query;

                    if (!uid) {
                        req.daoyiData = { error: "缺少使用者 uid" };
                        req.daoyiStatus = RESPONSE_BAD_REQUEST;
                        return next();
                    }

                    const docs = await queryService.getUserQueries(uid);

                    req.daoyiData = {
                        data: docs,
                        message: "成功取得使用者查詢",
                    };
                    req.daoyiStatus = RESPONSE_OK;
                    next();
                } catch (error) {
                    console.error("getUserQueries controller error:", error);
                    req.daoyiData = {
                        error: "取得使用者查詢時發生錯誤",
                        details: error.message,
                    };
                    req.daoyiStatus = RESPONSE_BAD_REQUEST;
                    next();
                }
            })
            .catch((error) => {
                console.log("getUserQueries auth error:", error);
                req.daoyiData = { error: "身份驗證失敗" };
                req.daoyiStatus = RESPONSE_BAD_REQUEST;
                next();
            });
    },

    /**
     * 取得公開的查詢
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next function
     */
    getPublicQueries: async (req, res, next) => {
        const authToken = req.headers.authorization;

        await verifyToken(authToken)
            .then(async () => {
                try {
                    const docs = await queryService.getPublicQueries();

                    req.daoyiData = {
                        data: docs,
                        message: "成功取得公開查詢",
                    };
                    req.daoyiStatus = RESPONSE_OK;
                    next();
                } catch (error) {
                    console.error("getPublicQueries controller error:", error);
                    req.daoyiData = {
                        error: "取得公開查詢時發生錯誤",
                        details: error.message,
                    };
                    req.daoyiStatus = RESPONSE_BAD_REQUEST;
                    next();
                }
            })
            .catch((error) => {
                console.log("getPublicQueries auth error:", error);
                req.daoyiData = { error: "身份驗證失敗" };
                req.daoyiStatus = RESPONSE_BAD_REQUEST;
                next();
            });
    },
};

module.exports = advancedQueryController;
