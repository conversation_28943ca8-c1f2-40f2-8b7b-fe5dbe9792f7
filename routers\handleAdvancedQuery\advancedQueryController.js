const queryService = require("./services/queryService");
const { RESPONSE_BAD_REQUEST, RESPONSE_OK } = require("../../config/config");

const advancedQueryController = {
    /**
     * 取得使用者的查詢
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next function
     */
    getUserQueries: async (req, res, next) => {
        try {
            const { uid } = req.query;

            if (!uid) {
                return res
                    .status(RESPONSE_BAD_REQUEST)
                    .send({ error: "缺少使用者 uid" });
            }

            const docs = await queryService.getUserQueries(uid);

            res.status(RESPONSE_OK).send({
                data: docs,
                message: "成功取得使用者查詢",
            });
        } catch (error) {
            console.error("getUserQueries controller error:", error);
            res.status(RESPONSE_BAD_REQUEST).send({
                error: "取得使用者查詢時發生錯誤",
                details: error.message,
            });
        }
    },

    /**
     * 取得公開的查詢
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {Function} next - Express next function
     */
    getPublicQueries: async (req, res, next) => {
        try {
            const docs = await queryService.getPublicQueries();

            res.status(RESPONSE_OK).send({
                data: docs,
                message: "成功取得公開查詢",
            });
        } catch (error) {
            console.error("getPublicQueries controller error:", error);
            res.status(RESPONSE_BAD_REQUEST).send({
                error: "取得公開查詢時發生錯誤",
                details: error.message,
            });
        }
    },
};

module.exports = advancedQueryController;
