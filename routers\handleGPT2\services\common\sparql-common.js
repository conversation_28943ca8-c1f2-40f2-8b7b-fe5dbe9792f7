const storeDriver = require("../sparql/storeDriver");
const { verifyToken } = require("../../../handleAuth/auth");
const { removeBindingsPrefix } = require("../sparql/rdf");
const { RESPONSE_OK } = require("../../../../config/config");
const { extractLang } = require("./common");

exports.doMakeApiQuery = (apiQuery, limit, offset) => {
    let newApiQuery = apiQuery;

    // // 將 SERVICE_NMTLWORDS 取代成有帳密的形式
    // newApiQuery = newApiQuery.replace(
    //     SERVICE_NMTLWORDS,
    //     storeDriverWords.servicePw()
    // );

    return new Promise((resolve, reject) => {
        storeDriver
            .makeQuery(newApiQuery, limit, offset)
            .then((response) => {
                const bindings = removeBindingsPrefix(
                    response.body.results.bindings
                );
                resolve({ data: bindings, head: response.body.head.vars });
            })
            .catch((error) => {
                reject({ error });
            });
    });
};

exports.doMakeCountQuery = (apiQuery) => {
    // Search count 以帶過來的參數決定要不要取
    // if (FEATURE_STORE_LIST.fuseki === FEATURE_STORE) {
    //     return null;
    // }
    let countQuery = `SELECT (COUNT(DISTINCT *) AS ?total) WHERE { { ${apiQuery} } }`;

    return new Promise((resolve, reject) => {
        storeDriver
            // .makeQuery(query, limit, offset, reasoning)
            .makeQuery(countQuery, -1, 0)
            .then((response) => {
                const bindings = removeBindingsPrefix(
                    response.body.results.bindings
                );
                resolve(bindings[0]);
            })
            .catch((error) => {
                reject({ error });
            });
    });
};

// 處理特殊字元,避免 sparql query 抱錯
const safeSparqlStr = (str) => {
    const split = (str || "").split("");
    const replace = [
        { from: "\\", to: "\\\\" },
        { from: "'", to: "\\'" },
    ];
    return split
        .map((s) => {
            let tmpS = s;
            replace.forEach(({ from, to }) => {
                tmpS = tmpS.replace(from, to);
            });
            return tmpS;
        })
        .join("");
};

exports.doSaprqlQuery = (queryStr) => {
    return new Promise((resolve, reject) => {
        storeDriver
            .makeQuery(queryStr)
            .then((response) => {
                if (response.status !== 200) {
                    console.log(response);
                }
                resolve(response);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

exports.doSaprqlUpdate = async (queryStr) => {
    return new Promise(async (resolve, reject) => {
        await storeDriver
            .makeUpdate(queryStr)
            .then((response) => {
                if (response.status !== 200) {
                    console.log(response);
                }
                resolve(response);
            })
            .catch((error) => {
                reject(error);
            });
    });
};

exports.doSplitMultipleValues = (value, opName, subjectId) => {
    let queryStr = "";

    // 如果為 null，則為刪除，不填值
    if (!value) {
        return "";
    }

    // Rule: Array is an array, string is a string.
    if (Array.isArray(value)) {
        value.forEach((an) => {
            // 如果為 null，則為刪除，不填值
            if (!an) {
                return;
            }

            const { nData, langTag } = extractLang(an);
            queryStr += `${subjectId} ${opName} '''${safeSparqlStr(
                nData
            )}'''${langTag} .`;
        });
    } else {
        const { nData, langTag } = extractLang(value);
        queryStr += `${subjectId} ${opName} '''${safeSparqlStr(
            nData
        )}'''${langTag} .`;
    }
    return queryStr;
};

exports.verifyTokenAndDoQuery = (
    { authToken, queryStr, limit, offset },
    callback
) => {
    return verifyToken(authToken)
        .then(() => {
            Promise.all([
                this.doMakeApiQuery(queryStr, limit, offset),
                this.doMakeCountQuery(queryStr),
            ])
                .then((value) => {
                    let resObj = {};
                    value.forEach((v) => {
                        resObj = Object.assign(resObj, v);
                    });
                    callback({ status: RESPONSE_OK, ...resObj });
                })
                .catch((err) => {
                    callback({ status: 400, data: { error: err.message } });
                });
        })
        .catch((error) => {
            console.log(error);
            callback({ status: 400, data: { error } });
        });
};

exports.safeSparqlStr = safeSparqlStr;
