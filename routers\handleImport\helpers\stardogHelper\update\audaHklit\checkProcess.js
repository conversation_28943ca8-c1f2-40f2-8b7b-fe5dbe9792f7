/*
 * 重要：Best_Known_Name 欄位必須有 @PER or @ORG
 * */

const errorHelper = require("../../../errorHelper");
const { DataColMap } = require("../../class.config");
const nameLibs = require("../../common/nameLibs");
const { safeGet } = nameLibs;
const checkProcess = async ({ mapList, nextNameIdPerson, nextNameIdOrg }) => {
    const newMapList = [...(mapList || [])];

    try {
        // todo:
        // 檢查資料每個欄位的 value
        // 0. 沒有 有bestKnownName, => block process
        // 1. 有bestKnownName, 沒有 nameId => 檢查資料庫是否有 nameId
        // => 1.1 若沒有則 create nameId
        // => 1.2 若有責使用資料庫的 nameId
        // 2. 有bestKnownName, 有nameId => 檢查 資料庫的 nameId 與檔案的 nameId 是否相同
        // => 2.1 若不同, 則 => block process
        // 3. 有 namedId, 沒有 localNameId => 依據 nameType 查詢資料庫的 localNameId
        // 3.1 若有該 nameType 的 localNameId, 則續編 localNameId
        // 3.2 若沒有該 nameType 的 localNameId, 則 create 新的 localNameId
        // 4. 沒有 nameId, 有 localNameId => block process

        let errorKey;
        let block = false;
        newMapList.forEach((row) => {
            if (block) return;
            // check bestKnowName
            const bestKnownName = safeGet(DataColMap.bestKnownName, row);
            const bstNameRequired = ["@PER", "@ORG"];
            let bstNameValidate = false;
            bstNameRequired.forEach((key) => {
                if (bestKnownName.indexOf(key) >= 0) {
                    bstNameValidate = true;
                }
            });
            if (!bstNameValidate) {
                errorKey = errorHelper.ERROR_CODE.BEST_KNOWN_NAME_ILLEGAL.name;
                block = true;
            }
        });

        return { errorKey, newMapList };
    } catch (err) {
        return { errorKey: errorHelper.ERROR_CODE.DEFAULT.name, newMapList };
    }
};

module.exports = {
    checkProcess,
};
