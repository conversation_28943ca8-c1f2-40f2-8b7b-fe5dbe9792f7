// config
const importConfig = require("../../config/config.import");
// helpers
const fileHelper = require("./helpers/fileHelper");
const errorHelper = require("./helpers/errorHelper");
const dbHelper = require("./helpers/dbHelper");
const stardogHelper = require("./helpers/stardogHelper/stardogHelper");
const mailHelper = require("./helpers/mailHelper");
const firebaseHelper = require("./helpers/firebaseHelper");
//
const { getFilePath } = fileHelper;
const { emailToUser, emailToManager } = mailHelper;

// 寫入主程序
const importMain = async (req, fileSheetRows, fileKey) => {
    const { userName, userEmail } = req.body;
    const user = { name: userName, email: userEmail };

    // 取得要處理的檔案路徑
    const filePath = getFilePath(req);
    // 取得 fileKey and fileName
    // realtime database specific path
    // rtDbPath: "/database/import";
    const rtDbPath = importConfig.firebaseRtDb.readWritePath;

    try {
        let rows =
            Array.isArray(fileSheetRows) &&
            fileSheetRows.length > 0 &&
            fileSheetRows[0].data;
        if (!rows) {
            // 若沒有提供 fileSheetRows, 可以從 filePath 讀取檔案
            const sheetRows = await fileHelper.readExcelFile(filePath);
            if (sheetRows && sheetRows[0] && sheetRows[0].data) {
                rows = sheetRows[0].data;
            }
        }

        if (!rows) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.FILE_ROWS_BLANK.name
            );
        }

        const mapList = fileHelper.arrayToMapList(rows.slice(1), rows[0]);

        // 確認要執行何種 import pattern
        const { pattern } = req.params;
        const patternSetting = importConfig.pattern[pattern];
        if (!patternSetting) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.PATTERN_NOT_EXIST.name
            );
        }
        // 寫入前, 先備份 database
        const backupRes = await dbHelper.databaseBackup(pattern);

        if (backupRes instanceof Error) {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.DATABASE_BACKUP_ERROR.name
            );
        } else {
            // eslint-disable-next-line no-unused-vars
            const { status, backupFileName } = backupRes;
            let rtDbDataObj, rtUpdateRes;

            // 備份成功的話, 才進行寫入程序
            // 開始寫入程序
            // 產生 queryString, 並匯入到 stardog
            if (!(pattern in stardogHelper.map)) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.NO_PATTERN_WRITER_METHOD.name
                );
            }
            // 依據 pattern 找到對應的寫入程序
            const writeMethod = stardogHelper[stardogHelper.map[pattern]];
            const stardogUpdateRes = await writeMethod(mapList, pattern);

            // 寫入 stardog 失敗
            if (stardogUpdateRes instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.WRITE_DATABASE_ERROR.name
                );
            } else {
                // 寫入 stardog 成功
                // 先取得 realtime database 設定檔
                rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
                // console.log("rtDbDataObj", rtDbDataObj);

                if (rtDbDataObj instanceof Error) {
                    return errorHelper.sendErrorCode(
                        errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                    );
                }
                // firebase 更新
                if (rtDbDataObj) {
                    const updateVal =
                        firebaseHelper.getRtDbUpdate4ImportSuccess({
                            data: rtDbDataObj,
                            fileKey,
                            backupFileName: backupFileName,
                        });
                    // 更新 realtime database 的資訊
                    rtUpdateRes = await firebaseHelper.rtDbUpdate(
                        rtDbPath,
                        updateVal
                    );
                }
                if (rtUpdateRes instanceof Error) {
                    return errorHelper.sendErrorCode(
                        errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                    );
                }

                // email 給使用者
                emailToUser(
                    user,
                    filePath,
                    mailHelper.mailMessage.status.success
                );
                // 通知管理者
                emailToManager(
                    user,
                    filePath,
                    mailHelper.mailMessage.status.success
                );
            }
        }
    } catch (err) {
        console.log("error:", err.message);
        let errObj = errorHelper.ERROR_CODE.DEFAULT;
        if (err.message in errorHelper.ERROR_CODE) {
            errObj = errorHelper.ERROR_CODE[err.message];
        }
        let rtDbDataObj, rtUpdateRes;
        try {
            // 先取得 realtime database 設定檔
            rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
            // console.log("rtDbDataObj", rtDbDataObj);

            if (rtDbDataObj instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                );
            }
            // firebase 更新
            if (rtDbDataObj) {
                const updateVal = firebaseHelper.getRtDbUpdate4ImportFail({
                    data: rtDbDataObj,
                    fileKey,
                    errorMessage: errObj.message,
                });
                // 更新 realtime database 的資訊
                rtUpdateRes = await firebaseHelper.rtDbUpdate(
                    rtDbPath,
                    updateVal
                );
            }
            if (rtUpdateRes instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                );
            }
        } catch (err) {
            console.log("err in exportMain", err.message);
        } finally {
            // email 給使用者
            emailToUser(user, filePath, mailHelper.mailMessage.status.failure);
            // 通知管理者
            emailToManager(
                user,
                filePath,
                mailHelper.mailMessage.status.failure
            );
        }
    }
};

module.exports = importMain;
