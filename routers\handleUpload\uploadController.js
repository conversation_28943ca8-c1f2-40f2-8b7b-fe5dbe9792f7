const { BAD_REQUEST } = require("../../commons/httpStatusCode");
const { CustomError } = require("../handleError");

const uploadController = {
    common: (req, res, next) => {
        // if (!req.fileValidation) return next(new CustomError(BAD_REQUEST));

        const message = `成功上傳 ${req.successCount} 個檔案, 失敗 ${req.failureCount} 個檔案`;
        return res.json({
            status: "success",
            message,
            successCount: req.successCount,
            failureCount: req.failureCount,
        });
    },
};

module.exports = uploadController;
