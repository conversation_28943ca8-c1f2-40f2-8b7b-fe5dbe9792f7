const { default: axios } = require("axios");
const {
    RESPONSE_OK,
    RESPONSE_BAD_REQUEST,
    ERROR_WRONG_PARAMS,
    ERROR_RESPONSE,
} = require("../../config/config");

const handleGIS = async (req, res) => {
    try {
        const { address } = req.body;

        if (!address) {
            return res.status(RESPONSE_BAD_REQUEST).json(ERROR_WRONG_PARAMS);
        }

        const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=AIzaSyAzmKuojVphdT1gL7h53HV2IY4LwCaBv3M`;

        const response = await axios.get(url);

        if (response.data.status !== "OK") {
            return res.status(RESPONSE_OK).json(null);
        }

        const location = response.data.results[0].geometry.location;

        return res.status(RESPONSE_OK).json(location);
    } catch (error) {
        return res.status(RESPONSE_BAD_REQUEST).json(ERROR_RESPONSE);
    }
};

module.exports = handleGIS;
