const fs = require("fs");
const Fs = fs.promises;
//
const xlsx = require("node-xlsx");
const timerHelper = require("./timerHelper");
const errorHelper = require("./errorHelper");
//
const importConfig = require("../../../config/config.import");

const deepClone = (object) => {
    return JSON.parse(JSON.stringify(object));
};

const STORE_FILE_MESSAGE = {
    success: "success",
    fail: "fail",
};

const fileHelper = {
    STORE_FILE_MESSAGE: STORE_FILE_MESSAGE,
    // 確認檔案是否存在
    existsFile: async (path) => {
        try {
            await Fs.access(path);
            return true;
        } catch (err) {
            return false;
        }
    },
    // read excel file
    // return: [
    //  {
    //      name: 'Sheet1',
    //      data: [
    //          [Array], [Array],
    //          [Array], [Array],
    //          [Array], [Array],
    //          [Array], [Array]
    //      ]
    //  }
    // ]
    readExcelFile: async (path) => {
        try {
            // Parse a file
            // [{
            //     name: 'Sheet1',
            //     data: [
            //       [Array], [Array], [Array], [], [], []
            //     ]
            // }]
            const workSheetsFromFile = await xlsx.parse(path);

            if (workSheetsFromFile) {
                // 移除 data 中的空白陣列
                const sheetDataRmBlank = workSheetsFromFile.reduce(
                    (acc, cur, idx) => {
                        if (cur && Array.isArray(cur.data)) {
                            acc[idx] = {};
                            acc[idx].name = cur.name;
                            acc[idx].data = cur.data.filter(
                                (dt) => Array.isArray(dt) && dt.length > 0
                            );
                        }
                        return acc;
                    },
                    []
                );
                return Promise.resolve(sheetDataRmBlank);
            } else {
                return Promise.resolve(false);
            }
        } catch (err) {
            console.log("readExcelFile", err.message);
            return Promise.reject(err.message);
        }
    },
    // read excel file cols
    getExcelCols: (rows) => {
        return rows[0];
    },
    // 比對檔案中的欄位是否與預設的欄位相同
    checkColsEqual: (cols, defaultCols) => {
        return (
            JSON.stringify(deepClone(cols).sort()) ===
            JSON.stringify(deepClone(defaultCols).sort())
        );
    },
    bestKnownNameChecker: (val) => {
        return val && (val.indexOf("@PER") >= 0 || val.indexOf("@ORG") >= 0);
    },
    // check 某個 col 的 value 是否符合設定
    // e.g. bestKnownName 必須是 @PER or @ORG
    validateSingleColVal: (mapList, key, checker) => {
        if (typeof checker !== "function") return true;
        return mapList.every((map) => {
            if (key in map) {
                return checker(map[key]);
            }
            return true;
        });
    },
    // 把 [["","",""],[],[],...] 轉變成 [{},{},...]
    arrayToMapList: (rows, headers) => {
        if (!(Array.isArray(rows) && Array.isArray(headers))) return [];
        return rows.reduce((acc, cur, idx) => {
            const curMap = headers.reduce((_acc, header, hIdx) => {
                _acc[header] = cur[hIdx];
                return _acc;
            }, {});
            acc.push(curMap);
            return acc;
        }, []);
    },
    getFilePath: (req) => {
        const { targetPath } = req;
        if (Array.isArray(targetPath) && targetPath[0]) {
            return targetPath[0];
        }
        return null;
    },
    // 取得檔案名稱
    getFileNameAndKey: (req) => {
        const { fileNames } = req;
        const { pattern } = req.params;
        const ts = Date.now();
        let graph = "";
        let database = "";
        if (pattern && pattern in importConfig.pattern) {
            graph = importConfig.pattern[pattern].graph;
            database = importConfig.pattern[pattern].getDatabase();
        }
        // 只取 pattern 第一個 item
        const patternStr = pattern || "";
        const safeFileName = patternStr.replace(/\//g, "_");
        const fileKey = `${database}-${graph}-${safeFileName}-${ts}`;
        const fileName = (Array.isArray(fileNames) && fileNames[0]) || null;

        return { fileKey, fileName };
    },
    // 確認要處理哪個檔案及檔案是否存在
    // - check req.fileValidation, req.targetPath 及是否有成功上傳
    fileValidate: async (req) => {
        const { targetPath } = req;
        if (Array.isArray(targetPath) && targetPath[0]) {
            const exist = await fileHelper.existsFile(targetPath[0]);
            return Promise.resolve(exist);
        }
        return Promise.resolve(false);
    },
    // store file to path
    writeFile: async (dataBuffer, fileDst) => {
        try {
            const startTime = new Date();
            timerHelper.timeStart("writeFile", startTime);

            // flag: 'w' => Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
            const err = await fs.promises.writeFile(fileDst, dataBuffer, {
                flag: "w",
            });
            if (err) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
                );
            } else {
                timerHelper.timeStampEnd("writeFile", startTime);
                return { status: STORE_FILE_MESSAGE.success };
            }
        } catch (err) {
            console.log("error in writeFile", err.message);
            return err;
        }
    },
};

module.exports = fileHelper;
