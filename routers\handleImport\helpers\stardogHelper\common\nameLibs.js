// allowed langauges
const allowedLanguages = ["@zh", "@en"];

const nameLibs = {
    // # IRI 的 ID 來自 BestKnowName, 必須置換不合法字元
    // # process bstKnownName for generating formal query string
    // # '<PERSON>' => '<PERSON><PERSON>'
    // # '．<PERSON>'  => '．<PERSON>'
    // # illegal characters: `^{}\|<> space
    formatIriId: (_name) => {
        let name = _name;
        if (!name) return "";
        // # replace hidden space
        name = name.replace(/\u200e/g, "");
        // reference of space unicode: https://www.compart.com/en/unicode/category/Zs
        // # replace space in front of sentence and in the end of sentence
        // name = name.trim().replace(/\s/g, "_");
        name = name
            .trim()
            .replace(
                /[\s\u0020\u00A0\u1680\u180e\u2000-\u2009\u200a\u200b\u202f\u205f\u3000]/g,
                "_"
            );
        // # bad_chars_list convert list
        const badCharsChange = {
            "^": "_",
            "\\": "_",
            "|": "_",
            "<": "_",
            ">": "_",
            "{": "_",
            "}": "_",
            '"': "_",
            "`": "_",
        };
        // # remove bad_chars
        Object.entries(badCharsChange).forEach(([pre, post]) => {
            // 置換全部的 bad character
            name = name.replace(pre, post);
            // fixme: 下面這個會出錯
            // name = name.replaceAll(pre, post);
        });
        return name;
    },

    // # format 用來儲存成 triple object 的值
    // # process word for generating legal query string
    // # 'John Smith' => 'John_Smith'
    // # '．Terence Cheung'  => '．Terence_Cheung'
    // # illegal characters: `^{}\|<> space
    formatTripleObj: (_name) => {
        let name = _name;
        // # replace hidden space
        // name = _name.replace(/\u200e/g, "");
        // # repalce space in front of sentence and in the end of sentence
        name = name.trim();

        // #     it is OK to place space in word
        // #     name = name.strip().replace(' ', '_')

        // # bad_chars_convert list
        const badCharsChange = {
            "\\": "\\\\", // # required => 寫入 \ 會有問題, 所以必須置換成 \\
            "'": "\\'", // # required => 寫入 ' 會有問題, 所以必須置換成 \\'
        };
        // # remove bad_chars
        Object.entries(badCharsChange).forEach(([pre, post]) => {
            name = name.replace(pre, post);
        });
        return name;
    },
    allowedLanguages: allowedLanguages,
    extractLang: (data) => {
        let nData = data;
        let langTag = "";

        allowedLanguages.forEach((lang) => {
            if (data.endsWith(lang)) {
                langTag = lang;
                nData = data.slice(0, -lang.length);
            }
        });

        return { nData, langTag };
    },
    safeGet: (key, obj) => {
        if (key in obj) return obj[key] || "";
        return "";
    },
};

module.exports = nameLibs;
