let apiObj = null;
let apiHelper = {};
let AppNameNumber = process.env.PORT || "Local";

module.exports = {
    getAppName() {
        // 根據 Firebase，如果要執行多個 app，在 initializeApp 時給第二個參數
        const timestamp = Date.now();
        return `${timestamp}_${AppNameNumber}`;
    },
    // apiObj
    setApiObj(api) {
        apiObj = api;
    },
    isApiObjNull() {
        return apiObj === null;
    },
    getInvertRelation() {
        return apiObj["read"]["relation/invert/list"]["1.0"];
    },
    getProperties() {
        return apiObj["read"]["property/list"]["1.0"];
    },
    isProduction() {
        return (
            !apiObj["api-config"].production ||
            apiObj["api-config"].production === "false"
        );
    },
    isDevelop() {
        return (
            !apiObj["api-config"].develop ||
            apiObj["api-config"].develop === "false"
        );
    },
    getToken() {
        return apiObj["api-config"].tokens.map((t) => t.token);
    },
    getApi(method, api) {
        if (apiObj === null) {
            return null;
        }
        return apiObj[method][api];
    },
    getApiExtend(api) {
        // console.log("getApiExtend", api);
        if (apiObj === null) {
            return null;
        }

        const prefixIdx = api.indexOf("/");
        if (prefixIdx < 0) {
            // default doc: read
            if ("read" in apiObj) {
                console.log('fallback to "read" doc');
                return apiObj["read"][api];
            }
            // default
            return null;
        }

        // 第一個斜線前面為文件目錄名稱
        // ex: backend/tlvmperiod/list/2.0
        // backend 為 api 目錄
        const apiDoc = api.slice(0, prefixIdx);
        // tlvmperiod/list/2.0
        const apiEle = api.slice(prefixIdx + 1);

        // console.log("apiDoc", apiDoc);
        // console.log("apiEle", apiEle);

        // 除了 api-config 以外，其它文件都是 API
        const ApiDocs = Object.keys(apiObj).filter((a) => a !== "api-config");

        // 在其它的 doc 找到
        if (ApiDocs.indexOf(apiDoc) > -1) {
            return apiObj[apiDoc][apiEle];
        }

        // default doc: read
        if ("read" in apiObj) {
            console.log('fallback to "read" doc');
            return apiObj["read"][api];
        }
        // default
        return null;
    },
    // apiHelper
    setApiHelper(helper) {
        apiHelper = helper;
    },
    getApiProperty() {
        if (apiHelper === null) {
            return null;
        }
        return apiHelper.property;
    },
    getApiRelation() {
        if (apiHelper === null) {
            return null;
        }
        return apiHelper.relation;
    },
};
