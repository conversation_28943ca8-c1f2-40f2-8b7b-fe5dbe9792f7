const fs = require("fs");
const path = require("path");
const JSZip = require("jszip");
//
const { getOs } = require("../../../commons/common");
const xlsx = require("node-xlsx");
const timerHelper = require("./timerHelper");
const errorHelper = require("./errorHelper");
//
const exportConfig = require("../../../config/config.export");
//
const fileHelper = {};

const dataObjHeadKey = "head";
const dataObjDataKey = "data";

// 取得檔案名稱
fileHelper.getFileNameAndKey = (file) => {
    const ts = Date.now();
    const { pattern } = file || {};
    let graph = "";
    let database = "";
    let extension = "";
    let zipExtension = "";
    if (pattern && pattern in exportConfig.pattern) {
        graph = exportConfig.pattern[pattern].graph;
        database = exportConfig.pattern[pattern].getDatabase();
        extension = exportConfig.pattern[pattern].extension;
        zipExtension = exportConfig.pattern[pattern].zipExtension;
    }
    // 只取 pattern 第一個 item
    const patternStr = Array.isArray(pattern) ? pattern[0] : pattern;
    const safeFileName = patternStr.replace(/\//g, "_");
    const fileKey = `${database}-${graph}-${safeFileName}-${ts}`;
    const fileName = `${fileKey}.${extension}`;
    const zipFileName = `${fileKey}.${zipExtension}`;

    return { fileKey, fileName, zipFileName };
};

// 取得存放檔案路徑
fileHelper.getDestination = (patternKey) => {
    if (
        !(
            patternKey in exportConfig.pattern &&
            exportConfig.pattern[patternKey].dir
        )
    ) {
        return;
    }

    const dir = exportConfig.pattern[patternKey].dir;
    return getOs() === "windows"
        ? path.join(process.env.FILE_SERVER_DST_WIN, dir || "")
        : path.join(process.env.FILE_SERVER_DST_LINUX, dir || "");
};

// 取得提取檔案 url
// e.g.https://.......
fileHelper.getFileUrlPath = (fileName, pattern) => {
    const patternStr =
        Array.isArray(pattern) && pattern.length > 0 ? pattern[0] : pattern;

    if (!(patternStr && patternStr in exportConfig.pattern)) return null;
    if (
        !(
            exportConfig.pattern[patternStr] &&
            exportConfig.pattern[patternStr].dir
        )
    )
        return null;

    const fileServerApiRoute = exportConfig.FILE_SERVER_DOWNLOAD_ROUTE;

    // 串接成 url
    // https://fs-root.daoyidh.com/ + database/download/hkbdb/ + export/auda_hklit
    return `${exportConfig.FILE_SERVER_BASE_URL}/${fileServerApiRoute}/${exportConfig.pattern[patternStr].dir}/${fileName}`;
};

// tsv to excel buffer
fileHelper.tsvToExcelBuffer = async (dataObj, patternStr) => {
    if (!(dataObjHeadKey in dataObj && dataObjDataKey in dataObj)) return null;
    // data: {
    //   data: [{}, {},...],
    //   head: [
    //         'Name_ID',       'Best_Known_Name',
    //         'Local_Name_ID', 'Name_Type',
    //         'Name',          'HKCAN_ID',
    //         'LCNAF',         'VIAF',
    //         'Wikidata',      'Year_of_Birth',
    //         'Year_of_Death', 'Remark_Name',
    //         'Remark_Year',   'Remark_Other'
    //   ]
    // }
    const startTime = new Date();
    timerHelper.timeStart("tsvToExcelBuffer", startTime);

    const xlsxData = [];
    if (!Array.isArray(dataObj[dataObjHeadKey])) return null;
    xlsxData.push(dataObj[dataObjHeadKey]);
    const headers = dataObj[dataObjHeadKey];
    if (Array.isArray(dataObj[dataObjDataKey])) {
        dataObj[dataObjDataKey].forEach((dt) => {
            const row = [];
            headers.forEach((hd) => {
                if (hd in dt) {
                    row.push(dt[hd]);
                } else {
                    row.push(null);
                }
            });
            xlsxData.push(row);
        });
    }
    // console.log("xlsxData", xlsxData);
    // const buffer = xlsx.build([{ name: "auda_hklit", data: xlsxData }]); // Returns a buffer
    const buffer = xlsx.build([{ name: patternStr, data: xlsxData }]); // Returns a buffer
    timerHelper.timeStampEnd("tsvToExcelBuffer", startTime);
    return buffer;
};

// 壓縮檔案
// return: node buffer
const zipFileToBuffer = async (fileName, buffer) => {
    try {
        const zip = new JSZip();
        zip.file(fileName, buffer, { binary: true });
        const zipBuffer = await zip.generateAsync({
            type: "nodebuffer",
            compression: "DEFLATE",
        });
        if (zipBuffer) {
            return zipBuffer;
        } else {
            return null;
        }
    } catch (err) {
        return err;
    }
};

const STORE_FILE_MESSAGE = {
    success: "success",
    fail: "fail",
};

fileHelper.STORE_FILE_MESSAGE = STORE_FILE_MESSAGE;

// store file to path
// fileName: zip 裡面的 檔案: filename.xlsx
// fileDst: zip 檔案路徑: /mnt/nmtl-files/aaaa/filename.zip
fileHelper.writeFile = async (fileName, dataBuffer, fileDst) => {
    try {
        const startTime = new Date();
        timerHelper.timeStart("writeFile", startTime);

        const zipBuffer = await zipFileToBuffer(fileName, dataBuffer);
        if (!(zipBuffer instanceof Error) && zipBuffer !== null) {
            // flag: 'w' => Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
            const err = await fs.promises.writeFile(fileDst, zipBuffer, {
                flag: "w",
            });
            if (err) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
                );
            } else {
                timerHelper.timeStampEnd("writeFile", startTime);
                return { status: STORE_FILE_MESSAGE.success };
            }
        } else {
            return errorHelper.sendErrorCode(
                errorHelper.ERROR_CODE.WRITE_FILE_ERROR.name
            );
        }
    } catch (err) {
        console.log("writeFile err.message", err.message);
        return err;
    }
};

module.exports = fileHelper;
