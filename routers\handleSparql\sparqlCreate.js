const { decodePersonId } = require("./services/common/common");

const { verifyToken } = require("../handleAuth/auth");
const createApiList = require("./services/crud/createApis");
const {
    ERROR_NO_PARAM_VER,
    ERROR_API_NOT_EXIST,
    ERROR_API_METHOD,
    ERROR_API_VER_NOT_EXIST,
    ERROR_NO_PAYLOAD,
    ERROR_LACK_OF_PARAMS,
} = require("../../config/config");

function sparqlCreate(req, res) {
    // auth
    const authToken = req.headers.authorization;
    //
    let { entry } = req.body;
    entry = decodePersonId(entry);

    // The update data is existing or not.
    if (!entry) {
        return res.status(400).send(ERROR_NO_PAYLOAD);
    }

    // The API has to have the version.
    const { ver } = req.params;
    if (!ver) {
        return res.status(400).send(ERROR_NO_PARAM_VER);
    }

    // The API method is not correct.
    const apiMethod = req.params[0];
    if (!apiMethod) {
        return res.status(400).send(ERROR_API_METHOD);
    }

    // The API method doesn't exist.
    const apiDef = createApiList[apiMethod];
    if (!apiDef) {
        return res.status(400).send(ERROR_API_NOT_EXIST);
    }

    // parameters required
    const lackParms = apiDef.required.find((r) => {
        return !entry.hasOwnProperty(r);
    });
    if (lackParms) {
        return res.status(400).send(ERROR_LACK_OF_PARAMS);
    }

    if (!apiDef.hasOwnProperty(ver)) {
        return res.status(400).send(ERROR_API_VER_NOT_EXIST);
    }
    const apiVer = apiDef[ver];

    verifyToken(authToken)
        .then(() => {
            apiVer(entry, (response, error) => {
                if (error) {
                    return res.status(400).send({ error });
                }
                const foundError = response.find((r) => {
                    return r.status !== 200;
                });
                if (foundError) {
                    return res
                        .status(foundError.status)
                        .send({ error: foundError.statusText });
                }
                return res.status(200).send({ data: "OK" });
            });
        })
        .catch((error) => {
            res.status(400).send({ error });
        });
}

module.exports = sparqlCreate;
