const {
    verifyTokenAndDoQuery,
} = require("../handleSparql/services/common/sparql-common");

const queryAndCount = async (req, res) => {
    const { query, limit, offset } = req.body;
    const authToken = req.headers.authorization;

    await verifyTokenAndDoQuery(
        { authToken, queryStr: query, limit, offset },
        ({ status, data, head, total }) => {
            res.status(status).send({ data, head, total });
        }
    );
};

module.exports = queryAndCount;
