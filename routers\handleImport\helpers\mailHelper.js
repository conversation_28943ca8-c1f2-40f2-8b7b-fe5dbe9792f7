const nodemailer = require("nodemailer");
const emailConfig = require("../../../config/config.email");
const { ERROR_WRONG_PARAMS } = require("../../../config/config");
const { getApi } = require("../../handleAuth/apiObj");

const mailMessage = {
    status: {
        success: "success",
        failure: "failure",
    },
    user: {
        success: (user, filePath) => ({
            from: emailConfig.auth.user,
            to: user.email,
            subject: `Import file to database successfully`,
            text: `${user.name} (${
                user.email
            }) import file successfully at ${new Date().toDateString()}.`,
        }),
        failure: (user, filePath) => ({
            from: emailConfig.auth.user,
            to: user.email,
            subject: "File import failure",
            text: `File import failure at at ${new Date().toDateString()}, please contact website developer.`,
        }),
    },
    manager: {
        success: (user, filePath, managerInfo) => ({
            from: emailConfig.auth.user,
            to: managerInfo.email,
            subject: `${user.name} (${user.email}) request importing file to database successfully`,
            text: `${user.name} (${
                user.email
            }) import file at ${new Date().toDateString()}. File path is ${filePath}`,
        }),
        failure: (user, filePath, managerInfo) => ({
            from: emailConfig.auth.user,
            to: managerInfo.email,
            subject: `${user.name} (${user.email}) request importing file to database failed`,
            text: `${user.name} (${
                user.email
            }) import file at ${new Date().toDateString()} failed. File path is ${filePath}`,
        }),
    },
};

const mailHelper = {
    mailMessage: mailMessage,

    // email 給使用者
    emailToUser: (user, filePath, status) => {
        if (status && status in mailHelper.mailMessage.user) {
            const sendMail = mailHelper.mailMessage.user[status](
                user,
                filePath
            );
            mailHelper.sendToUser(sendMail);
        }
    },

    // email 給管理者
    emailToManager: (user, filePath, status) => {
        const managerInfo = getApi("mail-config", "manager");

        if (managerInfo && status && status in mailHelper.mailMessage.manager) {
            const sendMail = mailHelper.mailMessage.manager[status](
                user,
                filePath,
                managerInfo
            );
            mailHelper.sendToUser(sendMail);
        }
    },

    // "sendMail": {
    // 	"from": "<EMAIL>",
    // 		"to": "<EMAIL>",
    // 		"subject": "Hello ✔",
    // 		"text": "Hello world?",
    // 		"html": "<b>Hello world?</b>"
    // }
    sendToUser: async (sendMail) => {
        try {
            if (
                !sendMail.hasOwnProperty("from") ||
                !sendMail.hasOwnProperty("to")
            ) {
                console.log("sendToUser:", ERROR_WRONG_PARAMS.error);
                return;
            }

            // create reusable transporter object using the default SMTP transport
            const transporter = nodemailer.createTransport(emailConfig);

            // send mail with defined transport object
            const _info = await transporter.sendMail(sendMail);
            if (_info) {
                console.log("sendMail success");
            }
        } catch (err) {
            console.log("sendToUser err:", err.message);
        }
    },
};

module.exports = mailHelper;
