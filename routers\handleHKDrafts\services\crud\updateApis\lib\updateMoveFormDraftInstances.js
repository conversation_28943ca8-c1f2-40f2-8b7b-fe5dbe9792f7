const { baseGraph, baseService } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");
const {
    user,
    password,
    url,
    db,
    draftDB,
} = require("../../../../../../config/config.fusekiDraft");

const moveFromDraftSparql = (draftGraph, service, targetGraph) => {
    return `
            INSERT {
              GRAPH ${targetGraph}{
                ?s ?p ?o .
              }
            }
            WHERE {
              SERVICE ${service} {
                GRAPH ${draftGraph} {
                  ?s ?p ?o.
                }
              }
            }
        `;
};

const moveFromDraftInstances = (entrySrc, entryDst) => {
    const _queryStr = moveFromDraftSparql(
        baseGraph(entryDst.value),
        baseService(
            `${user}:${password}@${url.replace(/^https?:\/\//, "")}/${draftDB}`
        ),
        baseGraph("maw")
    );
    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

exports.moveFromDraftInstances20 = (entrySrc, entryDst, callback) => {
    Promise.all([moveFromDraftInstances(entrySrc, entryDst)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
