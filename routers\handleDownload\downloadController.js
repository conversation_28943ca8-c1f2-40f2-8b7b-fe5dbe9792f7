const path = require("path");
//
const downloadConfig = require("../../config/config.download");
const errorHelper = require("./helpers/errorHelper");
const fileHelper = require("./helpers/fileHelper");
//
const { ERROR_CODE } = errorHelper;

const downloadController = {
    downloadTemplate: (req, res, next) => {
        const { pattern } = req.params;
        if (!(pattern && pattern in downloadConfig.pattern)) {
            const { code, message } = ERROR_CODE.PATTERN_NOT_EXIST;
            return res.status(code).json({
                statusCode: code,
                message: message,
            });
        }
        const patternConfig = downloadConfig.pattern[pattern];
        const { dir } = patternConfig || {};
        const filePath = path.join(process.cwd(), dir || "");
        // 確認是否存在該檔案
        if (!fileHelper.existsFile(filePath)) {
            const { code, message } = ERROR_CODE.TEMPLATE_FILE_NOT_EXIST;
            return res.status(code).json({
                statusCode: code,
                message: message,
            });
        }

        // 傳送檔案
        return res.sendFile(filePath, function (err) {
            if (err) {
                console.log("Sent file error", filePath);
                const { code, message } = ERROR_CODE.TEMPLATE_FILE_NOT_EXIST;
                return res.status(code).json({
                    statusCode: code,
                    message: message,
                });
            } else {
                console.log("Sent file success", filePath);
            }
        });
    },
};

module.exports = downloadController;
