const RelationEventType = "RelationEvent";
const NameNodeType = "NameNode";
const DateEventType = "DateEvent";

// instanceRandomID 為需要建立 random ID 的 class 列表
const instanceRandomID = [
    { prefix: "EVT", eventType: "Event" },
    { prefix: "AWEEVT", eventType: "AwardEvent" },
    { prefix: "ORGEVT", eventType: "OrganizationEvent" },
    { prefix: "EDUEVT", eventType: "EducationEvent" },
    { prefix: "EMPEVT", eventType: "EmploymentEvent" },
    { prefix: "RVEVT", eventType: "ReviewEvent" },
    { prefix: "ART", eventType: "Article" },
    { prefix: "PUB", eventType: "Publication" },
    { prefix: "OTW", eventType: "OtherWork" },
    // 以 localNameId 為 ID
    { prefix: "NNI", eventType: NameNodeType },
];

const eventPrefix = [
    { prefix: "EVT", eventType: "Event" },
    { prefix: "RELEVT", eventType: RelationEventType },
    { prefix: "AWEEVT", eventType: "AwardEvent" },
    { prefix: "ORGEVT", eventType: "OrganizationEvent" },
    { prefix: "EDUEVT", eventType: "EducationEvent" },
    { prefix: "EMPEVT", eventType: "EmploymentEvent" },
    { prefix: "RVEVT", eventType: "ReviewEvent" },
    { prefix: "NNI", eventType: NameNodeType },
];

const virtualEventPrefix = [{ prefix: "MEMEVT", eventType: "memberEvent" }];

const classPrefix = [
    { prefix: "PER", eventType: "Person" },
    { prefix: "PLA", eventType: "Place" },
    { prefix: "ORG", eventType: "Organization" },
    { prefix: "ART", eventType: "Article" },
    { prefix: "DAE", eventType: DateEventType },
    { prefix: "OTW", eventType: "OtherWork" },
    { prefix: "PUB", eventType: "Publication" },
    { prefix: "ADP", eventType: "AcademicDiscipline" },
    { prefix: "ADG", eventType: "AcademicDegree" },
    { prefix: "WEB", eventType: "WebConfig" },
].concat(eventPrefix, virtualEventPrefix);

module.exports = {
    RelationEventType,
    NameNodeType,
    DateEventType,
    instanceRandomID,
    eventPrefix,
    classPrefix,
};
