//
const nameLibs = require("../../common/nameLibs");
const {
    getNextNameIdStr,
    getLocalNameId,
    getNextLocalNameId,
    getNameTypeKeyByColVal,
} = require("./helper/nameNodeLib");
// 每個 class 的 property, (get from)
const {
    ClassConfig,
    DataColMap,
    PersonProp,
    OrganizationProp,
    NameNodeProp,
    AllowLangTagProp,
} = require("../../class.config");
//
const importConfig = require("../../../../../../config/config.import");
//
const errorHelper = require("../../../errorHelper");

//
const getPersonId = (_name) => {
    let name = _name.replace("@PER", "").replace("@ORG", "");
    nameLibs.allowedLanguages.forEach((lang) => {
        name = name.replace(lang, "");
    });
    return `<http://hkbdb.lib.cuhk.edu.hk/v1#PER${name}>`;
};

const getOrganizationId = (_name) => {
    let name = _name.replace("@PER", "").replace("@ORG", "");
    nameLibs.allowedLanguages.forEach((lang) => {
        name = name.replace(lang, "");
    });
    return `<http://hkbdb.lib.cuhk.edu.hk/v1#ORG${name}>`;
};

const getNameNodeId = (name) => {
    return `<http://hkbdb.lib.cuhk.edu.hk/v1#NNI${name}>`;
};

const safeGet = (key, obj) => {
    if (key in obj) return obj[key] || "";
    return "";
};

const isPersonName = (name) => {
    if (name) {
        if (name.indexOf("@PER") >= 0) return true;
        if (name.indexOf("@ORG") >= 0) return false;
        return null;
    }
    return null;
};

// # 依據 bestKnownName 判斷 person/organization
const getPerOrOrgClass = (name) => {
    try {
        // # 依據 bestKnownName 判斷 person/organization
        if (name) {
            // # 判斷為 person/organization
            let isPerson;
            // 從 bestKnowName 取得 @PER , @ORG
            isPerson = isPersonName(name);
            if (isPerson !== null) {
                return isPerson
                    ? ClassConfig.Person.name
                    : ClassConfig.Organization.name;
            }
            return "";
        } else {
            return "";
        }
    } catch (err) {
        console.log("ERROR EXCEPT!! IN getPerOrOrgClass()", name);
        return "";
    }
};

// # @客製化: 判別 instance 的 class
// # 若從資料中即可判別 instance class, 則可以直接 return 該 class
const getInstClass = (instName) => {
    try {
        // # 取得 bestKnownName, 並依據 bestKnownName 判斷 person/organization
        if (instName) {
            return getPerOrOrgClass(instName);
        }
    } catch (err) {
        console.log("ERROR EXCEPT!! IN getInstClass()", instName);
        return "";
    }
};

const insertData = (query, DataSet) => {
    return `
        INSERT DATA {
            GRAPH <http://hkbdb.lib.cuhk.edu.hk/v1/${DataSet}> {
               ${query}
            }
        }
    `;
};

// create person
const createPerson = (id, valItem) => {
    let qryStr = "";

    const _instanceId = id;

    // # 取得 person 所有的 property
    const curProps = Object.keys(PersonProp);

    let perId;
    if (_instanceId && _instanceId.length > 0) {
        perId = getPersonId(_instanceId);
        qryStr += `${perId} a hkbdb:Person .`;
    }

    let safeVal = "";
    const safeLabel = safeGet(PersonProp.label, valItem);
    if (safeLabel.length > 0) {
        safeVal = nameLibs.formatTripleObj(safeLabel);
        qryStr += `${perId} rdfs:label '''${safeVal}'''.`;
    }

    curProps.forEach((prop) => {
        safeVal = "";
        let safePropVal = "";
        safePropVal = safeGet(prop, valItem);
        if (prop.length > 0 && safePropVal.length > 0) {
            safeVal = nameLibs.formatTripleObj(safePropVal);
            qryStr += `${perId} hkbdb:${prop} '''${safeVal}'''. `;

            const { nData, langTag } = nameLibs.extractLang(safePropVal);
            safeVal = nameLibs.formatTripleObj(nData);
            // 依據設定夾帶語系標籤
            if (AllowLangTagProp.includes(prop)) {
                qryStr += `${perId} hkbdb:${prop} '''${safeVal}'''${langTag}. `;
            } else {
                qryStr += `${perId} hkbdb:${prop} '''${safeVal}'''. `;
            }
        }
    });

    return qryStr;
};

// create organization
const createOrganization = (id, valItem) => {
    let qryStr = "";

    const _instanceId = id;

    // # 取得 person 所有的 property
    const curProps = Object.keys(OrganizationProp);

    let perId;
    if (_instanceId && _instanceId.length > 0) {
        perId = getOrganizationId(_instanceId);
        qryStr += `${perId} a hkbdb:Organization .`;
    }

    let safeVal = "";
    const safeLabel = safeGet(OrganizationProp.label, valItem);
    if (safeLabel.length > 0) {
        safeVal = nameLibs.formatTripleObj(safeLabel);
        qryStr += `${perId} rdfs:label '''${safeVal}''' .`;
    }

    curProps.forEach((prop) => {
        safeVal = "";
        let safePropVal = "";
        safePropVal = safeGet(prop, valItem);
        if (prop.length > 0 && safePropVal.length > 0) {
            safeVal = nameLibs.formatTripleObj(safePropVal);

            const { nData, langTag } = nameLibs.extractLang(safePropVal);
            safeVal = nameLibs.formatTripleObj(nData);
            // 依據設定夾帶語系標籤
            if (AllowLangTagProp.includes(prop)) {
                qryStr += `${perId} hkbdb:${prop} '''${safeVal}'''${langTag}. `;
            } else {
                qryStr += `${perId} hkbdb:${prop} '''${safeVal}''' . `;
            }
        }
    });

    return qryStr;
};

// create nameNode
const createNameNode = (perOrgId, valItem) => {
    let qryStr = "";

    const _instanceId = perOrgId;
    const _instanceClass = getInstClass(perOrgId);
    let instanceId = "";
    if (_instanceClass === ClassConfig.Person.name) {
        instanceId = getPersonId(_instanceId);
    } else if (_instanceClass === ClassConfig.Organization.name) {
        instanceId = getOrganizationId(_instanceId);
    } else {
        console.log("require @PER or @ORG in bestKnowName");
    }

    const _nnId = safeGet(NameNodeProp.localNameId, valItem);
    const nnId = getNameNodeId(_nnId);

    // # 建立 person/orgainzation 與 nameNode 的連結
    if (
        _instanceId &&
        _instanceId.length > 0 &&
        _nnId &&
        _nnId.length > 0 &&
        instanceId.length > 0 &&
        nnId.length > 0
    ) {
        qryStr += `${instanceId} hkbdb:hasNameId ${nnId} . `;
    }

    // # 取得 NameNode 所有的 property
    const nnProps = Object.keys(NameNodeProp);

    // # 建立 NameNode 及其 property
    if (_nnId && _nnId.length > 0 && nnId.length > 0) {
        qryStr += `${nnId} a hkbdb:NameNode . `;
    }

    let safeVal = "";
    const safeLabel = safeGet(NameNodeProp.localNameId, valItem);
    if (safeLabel.length > 0) {
        safeVal = "";
        safeVal = nameLibs.formatTripleObj(safeLabel);
        qryStr += `${nnId} rdfs:label '''${safeVal}'''. `;
    }

    nnProps.forEach((prop) => {
        safeVal = "";
        let safePropVal = "";
        safePropVal = safeGet(prop, valItem);
        if (prop.length > 0 && safePropVal.length > 0) {
            const { nData, langTag } = nameLibs.extractLang(safePropVal);
            safeVal = nameLibs.formatTripleObj(nData);
            if (AllowLangTagProp.includes(prop)) {
                qryStr += `${nnId} hkbdb:${prop} '''${safeVal}'''${langTag}. `;
            } else {
                qryStr += `${nnId} hkbdb:${prop} '''${safeVal}'''. `;
            }
        }
    });
    return qryStr;
};

// # @ 客製化方法
// # 間接取得某個 val, e.g. 例如需要透過 2 個欄位才可以取得
// # nameType: 'best known name', 'original name', 'pen name'
const indirectGetName = (nameType, row) => {
    if (
        safeGet("Name_Type", row) &&
        safeGet("Name_Type", row) === nameType &&
        safeGet("Name", row)
    ) {
        return safeGet("Name", row);
    }
    return "";
};

const EXCEL_INST_ID_KEY = "Best_Known_Name";

// # 宣告方法
// # 在某些情況下, 取得 instance id, 可能需要使用特殊的方式取得, 可以在此設定
const getInstanceId = (row) => {
    try {
        if (safeGet(EXCEL_INST_ID_KEY, row)) {
            return nameLibs.formatIriId(safeGet(EXCEL_INST_ID_KEY, row)).trim();
        }
        return "";
    } catch (err) {
        console.log("ERROR EXCEPT!! IN getInstanceId()", row);
        return "";
    }
};

// 可依據資料量調整 INTERVAL, 會影響每次會寫入的 queryString 長度及 query 的次數
const INTERVAL_COUNT = 1000; // 多少筆為一個間隔

const queryStrProcess = ({
    mapList,
    pattern,
    nextNameIdPerson,
    nextNameIdOrg,
}) => {
    const queryStrList = [];
    let queryStr = "";
    const graph = importConfig.pattern[pattern].graph;
    //
    let _nextNameIdPerson = nextNameIdPerson;
    let _nextNameIdOrg = nextNameIdOrg;
    const getNextNameIdByClass = (curClass) => {
        if (
            ![ClassConfig.Person.name, ClassConfig.Organization.name].includes(
                curClass
            )
        )
            return null;
        let nextNameId;
        if (curClass === ClassConfig.Person.name) {
            nextNameId = _nextNameIdPerson;
            _nextNameIdPerson = getNextNameIdStr(_nextNameIdPerson);
        } else {
            nextNameId = _nextNameIdOrg;
            _nextNameIdOrg = getNextNameIdStr(_nextNameIdOrg);
        }
        return nextNameId;
    };

    // {
    // 金庸: {nameId: 300001, originalName:00002, penName: 01001,....  }
    // }
    const nameIdMap = {};

    let errorKey;
    // break loop when one error occur
    mapList.every(async (row, idx) => {
        // # 取得 instanceId, 用來建立 thisInstance 的 id
        const instanceId = getInstanceId(row);
        const instClass = getInstClass(instanceId);

        if (!instClass) {
            errorKey = errorHelper.ERROR_CODE.BEST_KNOWN_NAME_ILLEGAL.name;
            return false;
            // return errorHelper.sendErrorCode(
            //     errorHelper.ERROR_CODE.BEST_KNOWN_NAME_ILLEGAL.name
            // );
        }

        const valItem = {};

        // # 收集這個 instance 的所有資料
        valItem["bestKnownName"] = safeGet(DataColMap.bestKnownName, row)
            .replace("@PER", "")
            .replace("@ORG", "");
        //
        if (!(valItem["bestKnownName"] in nameIdMap)) {
            nameIdMap[valItem["bestKnownName"]] = {};
        }
        //
        valItem["nameId"] = safeGet(DataColMap.nameId, row);
        // nameId + 依據 nameType(pen name, zh, hao, original name, joinPenName) 取得編號
        valItem["localNameId"] = safeGet(DataColMap.localNameId, row).trim();
        // todo: get localNameId when instance already has localNameId
        if (
            (!valItem["nameId"] && valItem["localNameId"]) ||
            (valItem["nameId"] && !valItem["localNameId"])
        ) {
            errorKey =
                errorHelper.ERROR_CODE.NAME_ID_NOT_MATCH_LOCAL_NAME_ID.name;
            return false;
            // return errorHelper.sendErrorCode(
            //     errorHelper.ERROR_CODE
            //         .NAME_ID_NOT_MATCH_LOCAL_NAME_ID.name
            // );
        }

        const curNameIdMapObj = nameIdMap[valItem["bestKnownName"]];
        if (!valItem["nameId"]) {
            if ("nameId" in curNameIdMapObj) {
                valItem["nameId"] = curNameIdMapObj["nameId"];
            } else {
                // automatically add nameId, person(3xxxxxx), organization(4xxxxxx)
                const nameId = getNextNameIdByClass(instClass);
                // get nameId fail and break this loop
                if (!nameId) return;
                curNameIdMapObj["nameId"] = nameId;
                valItem["nameId"] = curNameIdMapObj["nameId"];
            }
        }

        valItem["nnBestKnownName"] = indirectGetName("best known name", row)
            .replace("@PER", "")
            .replace("@ORG", "");
        // special get: originalName, penName
        valItem["originalName"] = indirectGetName("original name", row);
        valItem["penName"] = indirectGetName("pen name", row);
        valItem["zi"] = indirectGetName("zi", row);
        valItem["hao"] = indirectGetName("hao", row);
        valItem["joinPenName"] = indirectGetName("join pen name", row);
        valItem["name"] = indirectGetName("name", row);

        const nameType = safeGet(DataColMap.nameType, row);
        const nameTypeKey = getNameTypeKeyByColVal(nameType);

        // automatically add localNameId:
        if (!valItem["localNameId"] && nameType && nameTypeKey) {
            let localNameId = getLocalNameId(valItem["nameId"], nameTypeKey);
            if (!(nameTypeKey in curNameIdMapObj)) {
                curNameIdMapObj[nameTypeKey] = localNameId;
            } else {
                localNameId = curNameIdMapObj[nameTypeKey];
            }
            curNameIdMapObj[nameTypeKey] = getNextLocalNameId(
                curNameIdMapObj[nameTypeKey],
                nameTypeKey
            );
            valItem["localNameId"] = localNameId;
        }

        valItem["hkcanId"] = safeGet(DataColMap.hkcanId, row).trim();
        valItem["lcnaf"] = safeGet(DataColMap.lcnaf, row).trim();
        valItem["viaf"] = safeGet(DataColMap.viaf, row).trim();
        valItem["remarkOther"] = safeGet(DataColMap.remarkOther, row).trim();
        valItem["wikidata"] = safeGet(DataColMap.wikidata, row).trim();
        valItem["remarkName"] = safeGet(DataColMap.remarkName, row).trim();
        valItem["remarkYear"] = safeGet(DataColMap.remarkYear, row).trim();
        valItem["yearOfBirth"] = safeGet(DataColMap.yearOfBirth, row).trim();
        valItem["yearOfDeath"] = safeGet(DataColMap.yearOfDeath, row).trim();

        // console.log(valItem);
        // console.log("instanceId", instanceId);
        // console.log("instClass", instClass);

        // return null;
        // # 建立 Person/Organization
        if (instClass === ClassConfig.Person.name) {
            queryStr += createPerson(instanceId, valItem);
        } else if (instClass === ClassConfig.Organization.name) {
            queryStr += createOrganization(instanceId, valItem);
        } else {
            errorKey = errorHelper.ERROR_CODE.BEST_KNOWN_NAME_ILLEGAL.name;
            return false;
            // return errorHelper.sendErrorCode(
            //     errorHelper.ERROR_CODE.BEST_KNOWN_NAME_ILLEGAL.name
            // );
        }

        // # 建立 NameNode
        queryStr += createNameNode(instanceId, valItem);

        // # 這個檔案寫入資料庫的時機不是按照 idx, 而是收集玩某個 instance 的全部資料後才寫入資料庫
        if (queryStr.length > 0 && idx % INTERVAL_COUNT === 0) {
            const insertQueryStr = insertData(queryStr, graph);
            queryStrList.push(insertQueryStr);
            queryStr = "";
        }

        if (idx % 10000 === 0) {
            console.log("idx:", idx);
        }
    });

    if (queryStr.length > 0) {
        const insertQueryStr = insertData(queryStr, graph);
        queryStrList.push(insertQueryStr);
    }

    return { errorKey, queryStrList };
};

module.exports = {
    queryStrProcess,
};
