const {
    maxInsIri,
    maxValue<PERSON>ri,
    maxValueGraph,
} = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const newInstance = (entry) => {
    const { prefix, number } = entry;

    const _queryStr = `
DELETE {
  GRAPH ${maxValueGraph()} {
    ${maxInsIri()} ${maxValueIri(prefix)} ?value .
  } 
}
INSERT {
  GRAPH ${maxValueGraph()} {
    ${maxInsIri()} ${maxValueIri(prefix)} ?newValue .
  } 
}
WHERE {
  {
    SELECT DISTINCT ?newValue
    {
      GRAPH ${maxValueGraph()} {
        OPTIONAL {
            ${maxInsIri()} ${maxValueIri(prefix)} ?value .
        }
        BIND(IF(EXISTS{${maxInsIri()} ${maxValueIri(prefix)} ?value}, (xsd:integer(?value) + xsd:integer(${number})), ${number}) AS ?newValue)
      } 
    } ORDER BY DESC(?newValue)
    LIMIT 1
  }
  GRAPH ${maxValueGraph()} {
    OPTIONAL {
        ${maxInsIri()} ${maxValueIri(prefix)} ?value .
    }
  } 
}
`;

    // console.log("_queryStr", _queryStr);
    return doSaprqlUpdate(_queryStr);
    // return new Promise((resolve) => resolve({ status: 200 }))
};

exports.newInstance20 = async (entry, callback) => {
    await Promise.all([newInstance(entry)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
