const path = require("path");
const fs = require("fs");
const { getOs } = require("../../../commons/common");
//
const storeDriver = require("../../handleSparql/services/sparql/storeDriver");
const fileHelper = require("./fileHelper");
const errorHelper = require("./errorHelper");
//
const importConfig = require("../../../config/config.import");
//
const { STORE_FILE_MESSAGE } = fileHelper;

// 依據系統取得備份檔案路徑
const getBackupFilePath = (dir, fileName) => {
    // filepath: basicRoot + directory + fileName + extension
    if (!dir) return null;

    // targetPath => e.g. /mnt/nmtl-static-files/hkbdb/backup
    const targetPath =
        getOs() === "windows"
            ? path.join(process.env.FILE_SERVER_DST_WIN, dir || "")
            : path.join(process.env.FILE_SERVER_DST_LINUX, dir || "");
    const filePath = path.join(targetPath, fileName);

    fs.mkdirSync(targetPath, { recursive: true }, () => {});

    return filePath;
};

const dbHelper = {
    databaseBackup: async (pattern) => {
        try {
            const { ERROR_CODE } = errorHelper;

            if (!pattern && pattern in importConfig.pattern) {
                return errorHelper.sendErrorCode(
                    ERROR_CODE.PATTERN_NOT_EXIST.name
                );
            }

            const patternSet = importConfig.pattern[pattern];
            const {
                getDatabase,
                BACKUP_GRAPH,
                getBackupFileName,
                getBackupMineType,
            } = patternSet;

            const backupDB = getDatabase();
            const backupGraph = BACKUP_GRAPH;
            const { dbBackupDir, DB_BACKUP_FILE_TYPE_EXT } = importConfig;
            let exportType = DB_BACKUP_FILE_TYPE_EXT.json;
            let fileName = getBackupFileName(exportType);
            let mineType = getBackupMineType(exportType);

            if (!(dbBackupDir && fileName && backupDB && backupGraph))
                return Promise.resolve(null);

            // 使用 exportDbGraph, 匯出時會包含 graph 及 graph 中的 triple
            // 若使用 exportGraph: 匯出時不包含 graph
            const exportMethod = storeDriver.exportDbGraph;

            return exportMethod(backupDB, backupGraph, mineType)
                .then(async (res) => {
                    if (!res) {
                        return errorHelper.sendErrorCode(
                            ERROR_CODE.DATABASE_BACKUP_ERROR.name
                        );
                    }
                    if (res && res.status !== 200) {
                        return errorHelper.sendErrorCode(
                            ERROR_CODE.DATABASE_BACKUP_ERROR.name
                        );
                    }

                    if (res.body) {
                        let dataBuffer;
                        if (typeof res.body === "string") {
                            dataBuffer = Buffer.from(res.body);
                        } else {
                            // res.body 若為 object, 變更 file extension 為 json
                            mineType = getBackupMineType(
                                DB_BACKUP_FILE_TYPE_EXT.json
                            );
                            fileName = getBackupFileName(
                                DB_BACKUP_FILE_TYPE_EXT.json
                            );
                            dataBuffer = Buffer.from(JSON.stringify(res.body));
                        }
                        const filePath = getBackupFilePath(
                            dbBackupDir,
                            fileName
                        );
                        const writeRes = await fileHelper.writeFile(
                            dataBuffer,
                            filePath
                        );
                        if (!(writeRes instanceof Error) && writeRes !== null) {
                            return {
                                status: "finish",
                                backupFileName: fileName,
                            };
                        } else {
                            return errorHelper.sendErrorCode(
                                ERROR_CODE.DATABASE_BACKUP_ERROR.name
                            );
                        }
                    } else {
                        return errorHelper.sendErrorCode(
                            ERROR_CODE.DATABASE_BACKUP_ERROR.name
                        );
                    }
                })
                .catch((err) => {
                    console.log("error in exportGraph", err.message);
                    return err;
                });
        } catch (err) {
            console.log("error in exportGraph", err.message);
            return err;
        }
    },
};

module.exports = dbHelper;
