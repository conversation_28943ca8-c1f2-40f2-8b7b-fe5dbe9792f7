const express = require("express");
const router = express.Router();
const upload = require("../../commons/multer");
const importController = require("./importController");
const { UPLOAD_FIELD } = require("../../config/config-upload");
const { verifyToken } = require("../handleAuth/auth");
const { UNAUTHORIZED } = require("../../commons/httpStatusCode");
const { CustomError } = require("../handleError");

const authHandler = (req, res, next) => {
    const authToken = req.headers.authorization;
    return verifyToken(authToken)
        .then(() => {
            next();
        })
        .catch((error) => {
            console.log(error);
            next(new CustomError(UNAUTHORIZED));
        });
};

router.post(
    "/file/:pattern",
    authHandler,
    upload(UPLOAD_FIELD.file.name).single(UPLOAD_FIELD.file.name),
    importController.importFile
);

module.exports = router;
