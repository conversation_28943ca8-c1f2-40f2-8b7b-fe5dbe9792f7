const { getFirebaseAdmin } = require("../handleAuth/auth");
const { RESPONSE_BAD_REQUEST, RESPONSE_OK } = require("../../config/config");

const userController = {
    getUserInfo: async (req, res, next) => {
        try {
            const auth = getFirebaseAdmin().auth();
            const { uid } = req.query;
            if (!uid) {
                return res
                    .status(RESPONSE_BAD_REQUEST)
                    .send({ error: "Lack of user uid" });
            }
            if (!auth) {
                return res
                    .status(RESPONSE_BAD_REQUEST)
                    .send({ error: "Server error" });
            }

            const user = await auth.getUser(uid);
            // {uid,email,emailVerified}
            res.status(RESPONSE_OK).send({
                user,
            });
        } catch (e) {
            res.status(RESPONSE_BAD_REQUEST).send({
                error: "Server error",
            });
        }
    },
    deleteUser: async (req, res, next) => {
        try {
            const auth = getFirebaseAdmin().auth();
            const { uid } = req.query;
            console.log("uid", uid);
            if (!uid) {
                return res
                    .status(RESPONSE_BAD_REQUEST)
                    .send({ error: "Lack of user uid" });
            }
            if (!auth) {
                return res
                    .status(RESPONSE_BAD_REQUEST)
                    .send({ error: "Server error" });
            }

            return auth.deleteUser(uid).then(() => {
                res.status(RESPONSE_OK).send({
                    status: "ok",
                });
            });
        } catch (e) {
            res.status(RESPONSE_BAD_REQUEST).send({
                error: "Server error",
            });
        }
    },
};

module.exports = userController;
