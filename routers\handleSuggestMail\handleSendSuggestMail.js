const schedule = require("node-schedule");
const handleMail = require('../handleMail/index')

async function handleSendSuggestMail (time,data) {
    const scheduledJob = schedule.scheduleJob(time, ()=>{
        handleMail({body:{sendMail:data}},null,(err) => {})
    })

    //  every 3 day
    // '0 38 14 */3 * *'

    //  every monday , Here the 1 refers to Monday in the week (0-6 are Sunday-Saturday).
    // '0 38 14 * * 1'

    //  every day at 14:38
    // '38 14 * * *'

}

module.exports = handleSendSuggestMail;