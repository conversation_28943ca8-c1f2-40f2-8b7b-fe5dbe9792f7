const express = require("express");
const router = express.Router();
const advancedQueryController = require("./advancedQueryController");
const { verifyToken } = require("../handleAuth/auth");
const { UNAUTHORIZED } = require("../../commons/httpStatusCode");
const { CustomError } = require("../handleError");

const authHandler = (req, res, next) => {
    const authToken = req.headers.authorization;
    return verifyToken(authToken)
        .then(() => {
            next();
        })
        .catch((error) => {
            console.log(error);
            next(new CustomError(UNAUTHORIZED));
        });
};

// 取得使用者的查詢
router.get(
    "/user-queries",
    authHandler,
    advancedQueryController.getUserQueries
);

// 取得公開的查詢
router.get(
    "/public-queries",
    authH<PERSON><PERSON>,
    advancedQueryController.getPublicQueries
);

module.exports = router;
