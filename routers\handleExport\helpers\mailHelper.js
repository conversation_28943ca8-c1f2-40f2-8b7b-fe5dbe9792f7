const nodemailer = require("nodemailer");
const emailConfig = require("../../../config/config.email");
const { ERROR_WRONG_PARAMS } = require("../../../config/config");
const siteConfig = require("../../../config/config.site");

const isProductionSite = process.env.MODE === "production";
const subjectPrefix = isProductionSite ? "" : "[TEST SITE]";
console.log("isProductionSite", isProductionSite);
console.log("subjectPrefix", subjectPrefix);

const backendSiteExportImportPage = siteConfig.backendSiteExportImportPage;

const mailMessage = {
    status: {
        success: "success",
        failure: "failure",
    },
    user: {
        success: (user, fileUrlPath) => ({
            from: emailConfig.auth.user,
            to: user.email,
            subject: `${subjectPrefix} File is ready to download`,
            text: `Please log in backend website ${backendSiteExportImportPage} and click download link.`,
        }),
        failure: (user, fileUrlPath) => ({
            from: emailConfig.auth.user,
            to: user.email,
            subject: `${subjectPrefix} File export failure`,
            text: `File export failure, please contact website developer.`,
        }),
    },
    manager: {
        success: (user, fileUrlPath, managerInfo) => ({
            from: emailConfig.auth.user,
            to: managerInfo.email,
            subject: `${subjectPrefix} ${user.name} (${user.email}) request exporting file`,
            text: `The file is ready to download. Please log in backend website ${backendSiteExportImportPage} and check it.`,
        }),
        failure: (user, fileUrlPath, managerInfo) => ({
            from: emailConfig.auth.user,
            to: managerInfo.email,
            subject: `${subjectPrefix} File export failure`,
            text: `File export failure, please check what problem is.`,
        }),
    },
};

const mailHelper = {};

mailHelper.mailMessage = mailMessage;

// "sendMail": {
// 	"from": "<EMAIL>",
// 		"to": "<EMAIL>",
// 		"subject": "Hello ✔",
// 		"text": "Hello world?",
// 		"html": "<b>Hello world?</b>"
// }
mailHelper.sendToUser = async (sendMail) => {
    try {
        if (
            !sendMail.hasOwnProperty("from") ||
            !sendMail.hasOwnProperty("to")
        ) {
            console.log("sendToUser:", ERROR_WRONG_PARAMS.error);
            return;
        }

        // create reusable transporter object using the default SMTP transport
        const transporter = nodemailer.createTransport(emailConfig);

        console.log("sendMail:", sendMail);

        // send mail with defined transport object
        const _info = await transporter.sendMail(sendMail);
        if (_info) {
            console.log("sendMail success");
        }
    } catch (err) {
        console.log("sendToUser err:", err.message);
    }
};

module.exports = mailHelper;
