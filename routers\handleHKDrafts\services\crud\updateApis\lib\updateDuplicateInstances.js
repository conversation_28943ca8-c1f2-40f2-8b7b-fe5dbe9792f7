const { baseIri } = require("../../../sparql/rdf");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const duplicateSparql = (iriSrc, iriDst) => {
    return `
        INSERT {
            GRAPH ?g {
                ?newS ?newP ?newO .
            }
        }
        WHERE {
            GRAPH ?g {
                {
                    BIND(${iriSrc} AS ?s) .
                    ?s ?p ?o .
                    BIND(${iriDst} AS ?newS) .
                    BIND(?p AS ?newP) .
                    BIND(?o AS ?newO) .
                }
                UNION 
                {
                    BIND(${iriSrc} AS ?o) .
                    ?s ?p ?o .
                    BIND(?s AS ?newS) .
                    BIND(?p AS ?newP) .
                    BIND(${iriDst} AS ?newO) .
                }
            }
        }
    `;
};

const duplicateUpdateSparql = (iriDst, dstName, idSrc, idDst) => {
    return `
        Delete {
            GRAPH ?g {
                ?id hkbdb:hasNameId ?nnId.
                ?id hkbdb:nameId ?nameId.
                ?id hkbdb:bestKnownName ?bestKnownName.
            }
        }
        Insert {
            GRAPH ?g {
                ?id hkbdb:nameId ?newNameId .
                ?id hkbdb:bestKnownName '''${dstName}'''.
            }
        }
        WHERE {
            GRAPH ?g {
                BIND(${iriDst} as ?id) .
                ?id hkbdb:bestKnownName ?bestKnownName.
                BIND('''${idSrc}''' AS ?nameId) .
                OPTIONAL {
                    ?id hkbdb:hasNameId ?nnId.
                }
                OPTIONAL {
                    ?id hkbdb:nameId ?nameId.
                    BIND(IF(STRLEN(?nameId)>0, '''${idDst}''', '') AS ?newNameId)
                }
            }
        };
    `;
};

const duplicateInstances = (entrySrc, entryDst) => {
    const _queryStr = duplicateSparql(
        baseIri(entrySrc.srcId),
        baseIri(entryDst.srcId)
    );
    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

const duplicateUpdateInstances = (entryDst, srcName, nameId, nameIdNumber) => {
    const _queryStr = duplicateUpdateSparql(
        baseIri(entryDst.srcId),
        srcName,
        nameId,
        nameIdNumber
    );
    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
};

exports.duplicateInstances20 = (
    entrySrc,
    entryDst,
    srcName,
    nameId,
    nameIdNumber,
    callback
) => {
    duplicateInstances(entrySrc, entryDst)
        .then(() => {
            duplicateUpdateInstances(entryDst, srcName, nameId, nameIdNumber)
                .then((values) => callback(values, null))
                .catch((err) => callback(null, err));
        })
        .catch((error) => {
            callback(null, error);
        });
};
