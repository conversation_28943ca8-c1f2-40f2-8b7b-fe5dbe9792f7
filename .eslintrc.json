{"env": {"node": true, "es6": true}, "extends": ["eslint:recommended", "standard", "prettier", "prettier/standard"], "plugins": ["prettier", "standard"], "parserOptions": {"ecmaVersion": 6, "ecmaFeatures": {"experimentalObjectRestSpread": true, "jsx": true}, "sourceType": "module"}, "rules": {"prettier/prettier": ["error", {"tabWidth": 4}], "indent": ["error", 4, {"SwitchCase": 1}], "no-console": "off"}}