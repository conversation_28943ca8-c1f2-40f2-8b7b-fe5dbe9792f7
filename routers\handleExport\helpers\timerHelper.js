const timerHelper = {};

const getSpendTime = (startDateTime, endDateTime) => {
    return endDateTime - startDateTime;
};

timerHelper.getSpendTime = getSpendTime;

timerHelper.timeStart = (title, startTime) => {
    console.log(`${title} start`, startTime);
};

timerHelper.timeStampEnd = (title, startTime) => {
    const endTime = new Date();
    const interval = getSpendTime(startTime, endTime);
    console.log(`${title} finish`, endTime);
    console.log(`${title} spend`, interval);
};

module.exports = timerHelper;
