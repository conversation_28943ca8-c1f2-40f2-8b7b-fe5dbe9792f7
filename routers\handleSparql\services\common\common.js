const { namespace } = require("../sparql/rdf");

const base64url = require("base64url");

const LOGIC = { and: "&&", or: "||", not: "&& !" };

exports.splitParams = (params) => {
    const match = /[\n]+/g.exec(params);

    // splitter includes '\n' and ','
    // '\n' has highest priority
    if (match) {
        return params.split("\n");
    }
    return params.split(",");
};

const getFilter = (repType, keywordArr) => {
    let keywordStr = "";
    const filterStr =
        "(CONTAINS(?title, '''[keyword]''') || CONTAINS(?desc, '''[keyword]'''))";
    const repQueryBox = new RegExp(`\\[keyword\\]`, "g");

    for (let idx = 0; idx < keywordArr.length; idx = idx + 1) {
        const keyword = keywordArr[idx].slice(0, 1);
        const types = keywordArr[idx].slice(1, -1);
        const logic = keywordArr[idx].slice(-1);

        const repKeyword = filterStr.replace(repQueryBox, keyword);
        if (idx === 0 && types.indexOf(repType) > -1) {
            keywordStr += repKeyword;
        } else if (types.indexOf(repType) > -1) {
            const logicStr = LOGIC.hasOwnProperty(logic) ? LOGIC[logic] : "&&";

            if (keywordStr.length > 0) {
                keywordStr += `${logicStr} ${repKeyword}`;
            } else {
                keywordStr += repKeyword;
            }
        }
    }
    return keywordStr.length > 0 ? `FILTER(${keywordStr}) .` : "";
};

const setLangauge = (queryStr, language) => {
    const repQueryBox = new RegExp(`\\[lang\\]`, "g");
    return queryStr.replace(repQueryBox, language);
};

exports.replaceQueryParams = (query, paramObj, key, language) => {
    if (typeof query === "object") {
        // multiple search
        if (
            query.hasOwnProperty("advancedSearch") &&
            query.hasOwnProperty("query")
        ) {
            let queryStr = "";

            const keyword1 = paramObj.hasOwnProperty("keyword1")
                ? paramObj["keyword1"]
                : "";
            const keyword2 = paramObj.hasOwnProperty("keyword2")
                ? paramObj["keyword2"]
                : "";
            const keyword3 = paramObj.hasOwnProperty("keyword3")
                ? paramObj["keyword3"]
                : "";

            const keyword1Arr = keyword1.split(",");
            const keyword2Arr = keyword2.split(",");
            const keyword3Arr = keyword3.split(",");

            Object.keys(query).forEach((repType) => {
                if (repType === "advancedSearch" || repType === "query") {
                    return;
                }
                let typeQryStr = query[repType];

                const filters = getFilter(repType, [
                    keyword1Arr,
                    keyword2Arr,
                    keyword3Arr,
                ]);

                if (filters.length > 0) {
                    typeQryStr += filters;
                    if (queryStr.length > 0) {
                        queryStr = `${queryStr} UNION {${typeQryStr}}`;
                    } else {
                        queryStr = `{${typeQryStr}}`;
                    }
                }
            });
            const repQueryBox = new RegExp(`\\[advancedSearch\\]`, "g");
            return setLangauge(
                query.query.replace(repQueryBox, queryStr),
                language
            );
        }

        // multiple section
        if (query.hasOwnProperty("query")) {
            let queryBoxStr = query.query;

            const prefix = query.hasOwnProperty("prefix")
                ? query["prefix"]
                : "hkbdb:";
            Object.keys(paramObj).forEach((param) => {
                if (param === "selection" || param === "selections") {
                    if (paramObj[param].length > 0) {
                        const selections = this.splitParams(paramObj[param]);
                        selections.forEach((sec) => {
                            const repQueryBox = new RegExp(`\\[${sec}\\]`, "g");
                            queryBoxStr = queryBoxStr.replace(
                                repQueryBox,
                                query[sec]
                            );
                        });
                    }
                }

                // replace the boxes with parameters
                if (query.hasOwnProperty(param)) {
                    if (
                        paramObj[param].length > 0 &&
                        paramObj[param] !== "all"
                    ) {
                        // replace the box
                        const repParam = new RegExp(`\\[${param}\\]`, "g");
                        queryBoxStr = queryBoxStr.replace(
                            repParam,
                            query[param]
                        );

                        // replace the sub-ids
                        let repValues = "";
                        const ids = this.splitParams(paramObj[param]);

                        let idsStr = "";
                        ids.forEach((id) => {
                            if (prefix === "") {
                                idsStr += `'''${id}''' `;
                            } else {
                                // 此寫法在某些 id 會報錯, e.g. hkbdb:ORG香港浸會學院(香港浸會大學)
                                // idsStr += `hkbdb:${id} `;

                                // 改成此寫法
                                idsStr += `<${namespace.base}${id}> `;
                            }
                        });
                        repValues += `VALUES ?${param} { ${idsStr} } .`;
                        queryBoxStr = queryBoxStr.replace(repParam, repValues);
                    }
                }
            });
            Object.keys(paramObj).forEach((param) => {
                const repParam = new RegExp(`\\[${param}\\]`, "g");
                if (param === "id") {
                    if (paramObj[param].length > 0) {
                        const repId = paramObj[param];
                        queryBoxStr = queryBoxStr.replace(repParam, repId);
                    }
                } else if (param === "ids") {
                    let repValues = "";
                    if (paramObj[param].length > 0) {
                        const ids = this.splitParams(paramObj[param]);
                        let idsStr = "";
                        ids.forEach((id) => {
                            if (prefix === "") {
                                idsStr += `'''${id}''' `;
                            } else {
                                idsStr += `${prefix}${id} `;
                            }
                        });
                        repValues += `VALUES ?${key} { ${idsStr} } .`;
                    }
                    queryBoxStr = queryBoxStr.replace(repParam, repValues);
                }
            });
            // replace the non-selected sectionBox#
            const leftBox = new RegExp(`\\[.*?\\]`, "g");
            return setLangauge(queryBoxStr.replace(leftBox, ""), language);
        }
    }

    let resQuery = query;
    Object.keys(paramObj).forEach((param) => {
        const repParam = new RegExp(`\\[${param}\\]`, "g");

        if (param === "dataset") {
            const graphs = this.splitParams(paramObj[param]);
            let repGraphs = "";

            if (graphs.indexOf("all") >= 0) {
                repGraphs = "";
            } else {
                let sameTerm = "";
                graphs.forEach((g, idx) => {
                    if (idx === 0) {
                        sameTerm += `SAMETERM(?graph, <http://hkbdb.lib.cuhk.edu.hk/v1/${g}>)`;
                        return;
                    }
                    sameTerm += ` || SAMETERM(?graph, <http://hkbdb.lib.cuhk.edu.hk/v1/${g}>) `;
                });
                repGraphs += `FILTER(${sameTerm}) .`;
            }
            resQuery = resQuery.replace(repParam, repGraphs);
        } else if (param === "ids") {
            let repValues = "";
            if (paramObj[param].length > 0) {
                const ids = this.splitParams(paramObj[param]);
                let idsStr = "";
                ids.forEach((id) => {
                    // 此寫法在某些 id 會報錯, e.g. hkbdb:ORG香港浸會學院(香港浸會大學)
                    // idsStr += `hkbdb:${id} `;

                    // 改成此寫法
                    idsStr += `<${namespace.base}${id}> `;
                });
                repValues += `VALUES ?${key} { ${idsStr} } .`;
            }
            resQuery = resQuery.replace(repParam, repValues);
        } else if (
            param === "personIds" ||
            param === "locationIds" ||
            param === "orgIds" ||
            param === "valueIds"
        ) {
            let repValues = "";
            if (paramObj[param].length > 0) {
                const ids = this.splitParams(paramObj[param]);
                let idsStr = "";
                ids.forEach((id) => {
                    idsStr += `hkbdb:${id} `;
                });
                repValues += idsStr;
            }
            resQuery = resQuery.replace(repParam, repValues);
        } else {
            if (param === "limit" || param === "offset") {
                return;
            }
            resQuery = resQuery.replace(repParam, paramObj[param]);
        }
    });
    return setLangauge(resQuery, language);
};

exports.isObjectEqual = (value, other) => {
    // Get the value type
    let type = Object.prototype.toString.call(value);

    // If the two objects are not the same type, return false
    if (type !== Object.prototype.toString.call(other)) return false;

    // If items are not an object or array, return false
    if (["[object Array]", "[object Object]"].indexOf(type) < 0) return false;

    // Compare the length of the length of the two items
    let valueLen =
        type === "[object Array]" ? value.length : Object.keys(value).length;
    let otherLen =
        type === "[object Array]" ? other.length : Object.keys(other).length;
    if (valueLen !== otherLen) return false;

    // Compare two items
    let compare = function (item1, item2) {
        // Get the object type
        let itemType = Object.prototype.toString.call(item1);

        // If an object or array, compare recursively
        if (["[object Array]", "[object Object]"].indexOf(itemType) >= 0) {
            if (!exports.isObjectEqual(item1, item2)) return false;
        }

        // Otherwise, do a simple comparison
        else {
            // If the two items are not the same type, return false
            if (itemType !== Object.prototype.toString.call(item2))
                return false;

            // Else if it's a function, convert to a string and compare
            // Otherwise, just compare
            if (itemType === "[object Function]") {
                if (item1.toString() !== item2.toString()) return false;
            } else {
                if (item1 !== item2) return false;
            }
        }
    };

    // Compare properties
    if (type === "[object Array]") {
        for (let i = 0; i < valueLen; i++) {
            if (compare(value[i], other[i]) === false) return false;
        }
    } else {
        for (let key in value) {
            if (value.hasOwnProperty(key)) {
                if (compare(value[key], other[key]) === false) return false;
            }
        }
    }

    // If nothing failed, return true
    return true;
};

exports.getYMD = (date) => {
    if (!date.includes("-")) {
        return [date, "", ""];
    }
    return date.split("-");
};

exports.paddingLeft = (str, length) => {
    if (str.length >= length) return str;
    else return this.paddingLeft("0" + str, length);
};

exports.convertToArray = (values) => {
    let resArray = [];
    if (!values) {
        return resArray;
    }

    if (Array.isArray(values)) {
        resArray = values;
    } else {
        resArray = values.split("/");
    }
    return resArray;
};

exports.map2SparqlLang = (lang) => {
    const langMap = { en: "en", "zh-tw": "zh", default: "zh" };
    let resLang = langMap["default"];
    if (langMap.hasOwnProperty(lang)) {
        resLang = langMap[lang];
    }
    return resLang;
};

const excDecoQueryParams = ["limit", "offset"];

exports.bs64DecodeQueryStr = (queryStrObj) => {
    Object.keys(queryStrObj).forEach((param) => {
        if (excDecoQueryParams.indexOf(param) < 0) {
            // Since "+" is reserved parameters, so it will not be in the query parameters.
            // We could map " " to "+" back.
            const correctedQry = queryStrObj[param].replace(" ", "+");
            queryStrObj[param] = base64url.decode(correctedQry);
        }
    });
    return queryStrObj;
};

exports.isEmpty = (obj) => Object.keys(obj || {}).length === 0;

const replaceIllegalChar = (name) => {
    if (this.isEmpty(name)) {
        return "";
    }
    // bad_chars_list convert list
    const badCharsChange = {
        " ": "_",
        "^": "_",
        // "\\\\": "_",
        "|": "_",
        "<": "_",
        ">": "_",
        "{": "_",
        "}": "_",
        '"': "_",
        "`": "_",
    };
    if (name.length > 0) name = name.trim();
    // # remove bad_chars
    Object.keys(badCharsChange).forEach((bc) => {
        const cvtChar = badCharsChange[bc];
        if (name && name.indexOf(bc) >= 0) {
            name = `${name || ""}`.replace(new RegExp(bc, "g"), cvtChar);
        }
    });

    return name.replace(/\\/gm, "_");
};

const illegalParamCheck = ["name"];

// replace illegal IRI character in query string
exports.replIllegalIRIChar = (queryStrObj) => {
    if (queryStrObj === undefined || queryStrObj === null) return queryStrObj;

    // String type
    if (typeof queryStrObj === "string") {
        return replaceIllegalChar(queryStrObj);
    }

    // Array type
    if (Array.isArray(queryStrObj)) {
        return queryStrObj.map((str) => replaceIllegalChar(str));
    }

    // Object type
    Object.keys(queryStrObj).forEach((param) => {
        if (illegalParamCheck.indexOf(param) >= 0) {
            queryStrObj[param] = replaceIllegalChar(queryStrObj[param]);
        }
    });
    return queryStrObj;
};

exports.decodePersonId = (entry) => {
    if (!entry.hasOwnProperty("srcId")) {
        return null;
    }

    const { srcId } = entry;
    if (this.isEmpty(srcId)) {
        return null;
    }
    const prefix = `${srcId}`.slice(0, 3);
    const person = `${srcId}`.slice(3);
    const oriPersonId = `${prefix}${base64url.decode(person)}`;
    if (this.isEmpty(oriPersonId)) {
        return null;
    }
    return {
        ...entry,
        srcId: oriPersonId,
    };
};

exports.decodeId = (entry, target = "srcId") => {
    if (!entry.hasOwnProperty(target)) {
        return entry;
    }

    const id = entry[target];
    if (this.isEmpty(id)) {
        return entry;
    }
    const prefix = `${id}`.slice(0, 3);
    const content = `${id}`.slice(3);
    const oriId = `${prefix}${base64url.decode(content)}`;
    if (this.isEmpty(oriId)) {
        return entry;
    }
    return {
        ...entry,
        [target]: oriId,
    };
};

// allowed langauges
const allowedLanguages = ["@zh", "@en"];
exports.extractLang = (data) => {
    let nData = data;
    let langTag = "";

    allowedLanguages.forEach((lang) => {
        if (data.endsWith(lang)) {
            langTag = lang;
            nData = data.slice(0, -lang.length);
        }
    });

    return { nData, langTag };
};

// NameNode localNameId
exports.localNameId = "localNameId__string";
