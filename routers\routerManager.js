// Sparql
const sparqlCreate = require("./handleSparql/sparqlCreate");
const sparqlRead = require("./handleSparql/sparqlRead");
const sparqlReadMassive = require("./handleSparql/sparqlReadMassive");
const sparqlUpdate = require("./handleSparql/sparqlUpdate");
const sparqlDelete = require("./handleSparql/sparqlDelete");
const sparqlDuplicate = require("./handleDuplicate/sparqlDuplicate");
// queryPage
const queryAndCount = require("./handleQueryPage/queryAndCount");
// merge
const sparqlMerge = require("./handleMerge/sparqlMerge");
// move
const sparqlMove = require("./handleMove/sparqlMove");
// move
const sparqlMoveFromDraft = require("./handleMoveFromDraft/sparqlMoveFromDraft");
// Health
const handleHealth = require("./handleHealth");
// Mail
const handleMail = require("./handleMail");
// GIS
const handleGIS = require("./handleGIS");
// user
const handleUser = require("./handleUser");
// upload
const handleUpload = require("./handleUpload");
// export
const handleExport = require("./handleExport");
// import
const handleImport = require("./handleImport");
// download
const handleDownload = require("./handleDownload");
// redis cache
const {
    redisGetCache,
    redisSetCache,
    redisGetMassiveCache,
    redisSetMassiveCache,
    redisFlushCache,
    redisDumpCache,
} = require("./handleRedis/handleRedis");
// hkbdb_draft
const hkDraftCreate = require("./handleHKDrafts/hkDraftCreate");
const hkDraftRead = require("./handleHKDrafts/hkDraftRead");
const hkDraftUpdate = require("./handleHKDrafts/hkDraftUpdate");
const hkDraftDelete = require("./handleHKDrafts/hkDraftDelete");
// gpt2
const gptCreate = require("./handleGPT2/sparqlCreate");
const gptRead = require("./handleGPT2/sparqlRead");
const gptUpdate = require("./handleGPT2/sparqlUpdate");
const gptDelete = require("./handleGPT2/sparqlDelete");
const storeDriverGPT2 = require("./handleGPT2/services/sparql/storeDriver");
// ai sparql query
const aiSparqlQueryRouter = require("./handleAiSparqlQuery/handleAiSparqlQuery");
// advanced query
const advancedQueryController = require("./handleAdvancedQuery/advancedQueryController");

function router(app) {
    // download template
    app.use("/download", handleDownload);
    // file upload
    app.use("/upload", handleUpload);
    // export
    app.use("/export", handleExport);
    // import
    app.use("/import", handleImport);
    // user
    app.use("/user", handleUser);
    // Health
    app.get("/health", handleHealth);
    // GIS
    app.post("/gis", handleGIS);
    // mail
    app.post("/mail", handleMail);
    // API cache
    app.get("/api/cache", redisDumpCache);
    app.get("/api/cache/flush", redisFlushCache);
    // merge
    app.put("/merge/*/:ver", redisFlushCache, sparqlMerge);
    app.put("/duplicate/*/:ver", redisFlushCache, sparqlDuplicate);
    // move
    app.put("/move/*/:ver", redisFlushCache, sparqlMove);
    // moveFromDraft
    app.put("/moveFromDraft/*/:ver", redisFlushCache, sparqlMoveFromDraft);
    // Sparql
    app.post(
        "/:lang/post/*/:ver",
        redisGetMassiveCache,
        sparqlReadMassive,
        redisSetMassiveCache
    );
    // ds: gpt2
    storeDriverGPT2.setSparql({});
    app.post("/gpt/:lang/*/:ver", redisFlushCache, gptCreate);
    app.get("/gpt/:lang/*/:ver", redisGetCache, gptRead, redisSetCache);
    app.put("/gpt/:lang/*/:ver", redisFlushCache, gptUpdate);
    app.delete("/gpt/:lang/*/:ver", redisFlushCache, gptDelete);
    // ds: hkbdb_draft
    app.post("/draft/:lang/*/:ver", redisFlushCache, hkDraftCreate);
    app.get("/draft/:lang/*/:ver", redisGetCache, hkDraftRead, redisSetCache);
    app.put("/draft/:lang/*/:ver", redisFlushCache, hkDraftUpdate);
    app.delete("/draft/:lang/*/:ver", redisFlushCache, hkDraftDelete);
    // ds: hkbdb
    app.post("/:lang/*/:ver", redisFlushCache, sparqlCreate);
    app.get("/:lang/*/:ver", redisGetCache, sparqlRead, redisSetCache);
    app.put("/:lang/*/:ver", redisFlushCache, sparqlUpdate);
    app.delete("/:lang/*/:ver", redisFlushCache, sparqlDelete);
    // queryPage
    app.post("/query", queryAndCount);
    app.post("/queryAndCount", queryAndCount);
    // ai sparql query
    app.post("/aiSparqlQuery", aiSparqlQueryRouter);
    // advanced query
    app.get(
        "/advanced-query/user-queries",
        redisGetCache,
        advancedQueryController.getUserQueries,
        redisSetCache
    );
    app.get(
        "/advanced-query/public-queries",
        redisGetCache,
        advancedQueryController.getPublicQueries,
        redisSetCache
    );
    app.post(
        "/advanced-query/insert",
        redisFlushCache,
        advancedQueryController.insertQuery
    );
}

module.exports = router;
