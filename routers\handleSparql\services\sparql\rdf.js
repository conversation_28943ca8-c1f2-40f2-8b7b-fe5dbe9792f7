const namespace = {
    graph: "http://hkbdb.lib.cuhk.edu.hk/v1/",
    base: "http://hkbdb.lib.cuhk.edu.hk/v1#",
    rdf: "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
    rdfs: "http://www.w3.org/2000/01/rdf-schema#",
    xsd: "http://www.w3.org/2001/XMLSchema#",
    owl: "http://www.w3.org/2002/07/owl#",
    text: "http://jena.apache.org/text#",
    http:"http://"
};
const iriPrefix = "hkbdb";
const MAX_INS_DEFINE = "MAX_INS";
const MAX_INS_PREFIX = "max_";
const MAX_VALUE_GRAPH = "settings";

const removePrefix = (iri) => {
    const found = Object.keys(namespace).find((key) => {
        const prefix = namespace[key];
        return iri.startsWith(prefix);
    });
    return iri.replace(new RegExp(namespace[found], "g"), "");
};

module.exports = {
    namespace,
    removeBindingsPrefix(bindings) {
        return bindings.map((b) => {
            const newBind = {};
            Object.keys(b).forEach((key) => {
                newBind[key] = removePrefix(b[key].value);
            });
            return newBind;
        });
    },
    baseIri(value) {
        return `<${namespace.base}${value}>`;
    },
    baseGraph(value) {
        return `<${namespace.graph}${value}>`;
    },
    baseService(value) {
        return `<${namespace.http}${value}>`;
    },
    rdfsIri(value) {
        return `<${namespace.rdfs}${value}>`;
    },
    maxInsIri() {
        return `<${namespace.base}${MAX_INS_DEFINE}>`;
    },
    maxValueIri(prefix) {
        return `<${namespace.base}${MAX_INS_PREFIX}${prefix}>`;
    },
    maxValueGraph() {
        return `<${namespace.graph}${MAX_VALUE_GRAPH}>`;
    },
    prjIri() {
        return iriPrefix;
    },
    prefixesIri() {
        return `
PREFIX ${iriPrefix}: <${namespace.base}>
PREFIX rdf: <${namespace.rdf}>
PREFIX rdfs: <${namespace.rdfs}>
PREFIX xsd: <${namespace.xsd}>
PREFIX owl: <${namespace.owl}>
PREFIX text: <${namespace.text}>`;
    },
};
