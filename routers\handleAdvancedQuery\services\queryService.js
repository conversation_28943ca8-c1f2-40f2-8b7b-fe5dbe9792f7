const firebase = require("../../../config/config.firebase");

const queryService = {
    /**
     * 取得使用者的查詢
     * @param {string} uid - 使用者 ID
     * @returns {Promise<Array>} 查詢結果陣列
     */
    getUserQueries: (uid) => {
        return firebase
            .firestore()
            .collection("storedQuery")
            .where("author.uid", "==", uid)
            .get()
            .then((snapshots) => {
                const docs = [];
                snapshots.forEach((snapshot) => {
                    const data = snapshot.data();
                    // snapshot.data 不包含文件 ID，所以要另外取出並塞回 data
                    data.id = snapshot.id;
                    docs.push(data);
                });
                return docs;
            })
            .catch((error) => {
                console.error("queryService.getUserQueries:error: ", error);
                return [];
            });
    },

    /**
     * 取得公開的查詢
     * @returns {Promise<Array>} 查詢結果陣列
     */
    getPublicQueries: () => {
        return firebase
            .firestore()
            .collection("storedQuery")
            .where("private", "==", false)
            .get()
            .then((snapshots) => {
                console.log("getPublicQueries", snapshots);
                const docs = [];
                snapshots.forEach((snapshot) => {
                    const data = snapshot.data();
                    // snapshot.data 不包含文件 ID，所以要另外取出並塞回 data
                    data.id = snapshot.id;
                    docs.push(data);
                });
                return docs;
            })
            .catch((error) => {
                console.error("queryService.getPublicQueries:error: ", error);
                return [];
            });
    },
};

module.exports = queryService;
