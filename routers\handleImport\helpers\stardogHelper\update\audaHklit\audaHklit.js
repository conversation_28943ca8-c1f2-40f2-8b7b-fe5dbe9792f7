const {
    doSaprqlQuery,
} = require("../../../../../handleSparql/services/common/sparql-common");
//
const { getNextNameId } = require("./helper/nameNodeLib");
// 每個 class 的 property, (get from)
const { ClassConfig } = require("../../class.config");
//
const errorHelper = require("../../../errorHelper");
//
const { checkProcess } = require("./checkProcess");
const { queryStrProcess } = require("./queryStrProcess");

const sleep = (ms) => {
    return new Promise((resolve) => setTimeout(resolve, ms));
};

// 可依據資料量調整 INTERVAL, 會影響每次會寫入的 queryString 長度及 query 的次數
const INTERVAL_SECONDS = 1000; // query 之間要隔多久

// 多久要 query 一次
const getInterval = (index, intervalLen = 1) => {
    return Math.floor(index / intervalLen) * INTERVAL_SECONDS;
};

const updateAudaHklit = async (mapList, pattern) => {
    try {
        let nextNameIdPersonQuery = await getNextNameId(
            ClassConfig.Person.name
        );
        let nextNameIdOrgQuery = await getNextNameId(
            ClassConfig.Organization.name
        );

        let timeConsume = 0;
        if (!Array.isArray(mapList)) return "finish";

        return Promise.all([nextNameIdPersonQuery, nextNameIdOrgQuery]).then(
            async (res) => {
                let nextNameIdPerson, nextNameIdOrg;
                nextNameIdPerson = res[0];
                nextNameIdOrg = res[1];

                // block process if nextNameIdPerson or nextNameIdOrg is null
                if (!(nextNameIdPerson && nextNameIdOrg)) {
                    return errorHelper.sendErrorCode(
                        errorHelper.ERROR_CODE.GET_NEXT_NAME_ID_ERROR.name
                    );
                }

                /* ============ 1.check process ============ */
                console.log("1.check process");
                const { errorKey: psErrKey, newMapList } = await checkProcess({
                    mapList,
                    nextNameIdPerson,
                    nextNameIdOrg,
                });
                if (psErrKey) {
                    return errorHelper.sendErrorCode(psErrKey);
                }

                /* ============ 2.create queryString process ============ */
                console.log("2.create queryString process");
                const { errorKey: qsErrKey, queryStrList } = queryStrProcess({
                    mapList: newMapList,
                    pattern: pattern,
                    nextNameIdPerson: nextNameIdPerson,
                    nextNameIdOrg: nextNameIdOrg,
                });

                if (qsErrKey) {
                    return errorHelper.sendErrorCode(qsErrKey);
                }

                /* ============ 3.write to stardog process ============ */
                console.log("3.write to stardog process");

                const updateQueries = [];
                queryStrList.forEach((qs, idx) => {
                    const interval = getInterval(idx);
                    // console.log("interval", interval);
                    timeConsume =
                        interval > timeConsume ? interval : timeConsume;

                    // query 之間要間隔, 避免 stardog server crash
                    sleep(interval).then(async () => {
                        updateQueries.push(await doSaprqlQuery(qs));
                    });
                });

                console.log("timeConsume", timeConsume);
                // 休眠一段時間後, 再引入 Promise.all
                return sleep(timeConsume).then(() => {
                    // 要注意 Promise.all 的 limit 限制: > 2097150 會出錯
                    // 只要一個 promise error 則停止
                    return Promise.all(updateQueries)
                        .then((responses) => {
                            console.log(
                                "updateQueries length",
                                updateQueries.length
                            );
                            console.log(
                                "Promise responses length",
                                responses.length
                            );
                            return "finish";
                        })
                        .catch((err) => {
                            console.log("Promise", err);
                            errorHelper.sendErrorCode(
                                errorHelper.ERROR_CODE.WRITE_DATABASE_ERROR.name
                            );
                        });
                });
            }
        );
    } catch (err) {
        console.log("err in updateMain:", err.message);
        return err;
    }
};

module.exports = updateAudaHklit;
