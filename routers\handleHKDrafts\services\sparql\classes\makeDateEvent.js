const { namespace, baseIri } = require("../rdf");
const { paddingLeft, getYMD } = require("../../common/common");

// =======================================
// Date Event
// =======================================
const ymdType = { year: 0, month: 1, day: 2 };

const getDate = (ymd) => {
    // const year = ymd[ymdType.year] ? paddingLeft(ymd[ymdType.year], 4) : "0000";
    const year = ymd[ymdType.year] ? ymd[ymdType.year] : "0000";
    const month = ymd[ymdType.month]
        ? paddingLeft(ymd[ymdType.month], 2)
        : "00";
    const day = ymd[ymdType.day] ? paddingLeft(ymd[ymdType.day], 2) : "00";

    return `${year}-${month}-${day}`;
};

const getDateStr = (dateStr) => {
    const startDate = getYMD(dateStr);
    return [startDate, startDate, `${getDate(startDate)}`];
};

const getPeriodStr = (periods) => {
    const startDate = getYMD(periods[0]);
    const endDate = getYMD(periods[1]);
    return [startDate, endDate, `${getDate(startDate)}_${getDate(endDate)}`];
};

const createPeriod = (start, end, opName) => {
    const propertyObject = [];
    const startNum = start === "" ? null : parseInt(start, 10);
    const endNum = end === "" ? null : parseInt(end, 10);

    if (startNum && endNum && endNum > startNum) {
        for (let idx = startNum; endNum >= idx; idx++) {
            propertyObject.push(`${baseIri(opName)} "${idx}"^^xsd:integer .`);
        }
    } else if (startNum && startNum > 0) {
        propertyObject.push(`${baseIri(opName)} "${startNum}"^^xsd:integer .`);
    }
    return propertyObject;
};

const createDate = (dateStr, type, bindId, className, classPrefix) => {
    dateStr = dateStr.replace("、", ".").replace(",", ".");

    // date could be period or piece of time
    let pieces = [];
    if (dateStr.includes(".")) {
        pieces = pieces.concat(dateStr.split("."));
    } else {
        pieces.push(dateStr);
    }

    let proertyObjects = [];
    let dateEvt = "";
    pieces.forEach((piece) => {
        if (piece.includes("~")) {
            // Period
            [startDate, endDate, evtStr] = getPeriodStr(piece.split("~"));
        } else if (piece.includes("_")) {
            // Period
            [startDate, endDate, evtStr] = getPeriodStr(piece.split("_"));
        } else {
            [startDate, endDate, evtStr] = getDateStr(piece);
        }

        if (dateEvt.length > 0) {
            dateEvt += "." + evtStr;
        } else {
            dateEvt = evtStr;
        }

        proertyObjects = proertyObjects.concat(
            createPeriod(startDate[ymdType.year], endDate[ymdType.year], "year")
        );
        proertyObjects = proertyObjects.concat(
            createPeriod(
                startDate[ymdType.month],
                endDate[ymdType.month],
                "month"
            )
        );
        proertyObjects = proertyObjects.concat(
            createPeriod(startDate[ymdType.day], endDate[ymdType.day], "day")
        );
    });

    let query = "";
    const dateEvent = `<${namespace.base}${classPrefix}_${dateEvt}>`;
    query += `${bindId} ${type} ${dateEvent} .`;
    query += `${dateEvent} a ${baseIri(className)} .`;

    proertyObjects.forEach((po) => {
        query += `${dateEvent} ${po}`;
    });

    return query;
};

const deleteDate = (dateStr, type, bindId, classPrefix) => {
    dateStr = dateStr.replace("、", ".").replace(",", ".");

    // date could be period or piece of time
    let pieces = [];
    if (dateStr.includes(".")) {
        pieces = pieces.concat(dateStr.split("."));
    } else {
        pieces.push(dateStr);
    }

    let dateEvt = "";
    pieces.forEach((piece) => {
        if (piece.includes("~")) {
            // Period
            [startDate, endDate, evtStr] = getPeriodStr(piece.split("~"));
        } else if (piece.includes("_")) {
            // Period
            [startDate, endDate, evtStr] = getPeriodStr(piece.split("_"));
        } else {
            [startDate, endDate, evtStr] = getDateStr(piece);
        }

        if (dateEvt.length > 0) {
            dateEvt += "." + evtStr;
        } else {
            dateEvt = evtStr;
        }
    });

    let query = "";
    const dateEvent = `<${namespace.base}${classPrefix}_${dateEvt}>`;
    query += `${bindId} ${type} ${dateEvent} .`;

    return query;
};

exports.createDateEvent = (
    parentId,
    dateOp,
    dateVal,
    className,
    classPrefix
) => {
    return createDate(dateVal, dateOp, parentId, className, classPrefix);
};

exports.deleteDateEvent = (parentId, dateOp, dateVal, classPrefix) => {
    return deleteDate(dateVal, dateOp, parentId, classPrefix);
};
