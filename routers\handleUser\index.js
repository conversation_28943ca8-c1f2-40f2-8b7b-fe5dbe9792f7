const express = require("express");
const router = express.Router();
const userController = require("./userController");
const { verifyToken } = require("../handleAuth/auth");
const { UNAUTHORIZED } = require("../../commons/httpStatusCode");
const { CustomError } = require("../handleError");

const authHandler = (req, res, next) => {
    const authToken = req.headers.authorization;
    return verifyToken(authToken)
        .then(() => {
            next();
        })
        .catch((error) => {
            console.log(error);
            next(new CustomError(UNAUTHORIZED));
        });
};

// 取得 authentication 的 user info
router.get("/info", authHandler, userController.getUserInfo);

// 刪除 authentication 的 user account
router.delete("/authAccount", authHandler, userController.deleteUser);

module.exports = router;
