const { generic20 } = require("./lib/deleteGeneric");
const { deletePerson20 } = require("./lib/deletePerson");
const { deleteGraph20 } = require("./lib/deleteGraph");
const { deleteEvent20 } = require("./lib/deleteEvent");

const index = {
    generic: { "2.0": generic20, required: ["graph"] },
    deletePerson: {
        "1.0": deletePerson20,
        "2.0": deletePerson20,
        required: [],
    },
    deleteGraph: { "1.0": deleteGraph20, required: ["graph"] },
    deleteEvent: { "1.0": deleteEvent20, required: ["graph"] },
};

module.exports = index;
