const {
    FEATURE_STORE,
    FEATURE_STORE_LIST,
} = require("../../../../config/config");
const storeDriverDraft = { setSparql: null, makeQuery: null };

switch (FEATURE_STORE) {
    case FEATURE_STORE_LIST.stardog: {
        const stardog = require("./storage/stardog");
        storeDriverDraft.setSparql = stardog.setSparql;
        storeDriverDraft.makeQuery = stardog.makeQuery;
        storeDriverDraft.makeUpdate = stardog.makeQuery;
        storeDriverDraft.getDbName = stardog.getDbName;
        storeDriverDraft.exportDbGraph = stardog.exportDbGraph;
        break;
    }
    case FEATURE_STORE_LIST.fuseki: {
        const fuseki = require("./storage/fuseki");
        storeDriverDraft.setSparql = fuseki.setSparql;
        storeDriverDraft.makeQuery = fuseki.makeQuery;
        storeDriverDraft.makeUpdate = fuseki.makeUpdate;
        storeDriverDraft.getDbName = fuseki.getDbName;
        storeDriverDraft.exportDbGraph = fuseki.exportDbGraph;
        break;
    }
}

module.exports = storeDriverDraft;
