const {
    FEATURE_STORE,
    FEATURE_STORE_LIST,
} = require("../../../../config/config");
const storeDriver = { setSparql: null, makeQuery: null };

switch (FEATURE_STORE) {
    case FEATURE_STORE_LIST.stardog: {
        const stardog = require("./storage/stardog");
        storeDriver.setSparql = stardog.setSparql;
        storeDriver.makeQuery = stardog.makeQuery;
        storeDriver.makeUpdate = stardog.makeQuery;
        storeDriver.getDbName = stardog.getDbName;
        storeDriver.exportDbGraph = stardog.exportDbGraph;
        break;
    }
    case FEATURE_STORE_LIST.fuseki: {
        const fuseki = require("./storage/fuseki");
        storeDriver.setSparql = fuseki.setSparql;
        storeDriver.makeQuery = fuseki.makeQuery;
        storeDriver.makeUpdate = fuseki.makeUpdate;
        storeDriver.getDbName = fuseki.getDbName;
        storeDriver.exportDbGraph = fuseki.exportDbGraph;
        break;
    }
}

module.exports = storeDriver;
