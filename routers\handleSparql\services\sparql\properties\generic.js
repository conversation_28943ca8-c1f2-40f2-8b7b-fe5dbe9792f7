const { namespace, baseIri, rdfsIri } = require("../rdf");
const {
    RelationEventType,
    NameNodeType,
    DateEventType,
    instanceRandomID,
    eventPrefix,
    classPrefix,
} = require("../classPrefix");
const { createDateEvent, deleteDateEvent } = require("../classes/makeDateEvent");
const { getApiProperty, getApiRelation } = require("../../../../handleAuth/apiObj");
const { isObjectEqual, replIllegalIRIChar, isEmpty, localNameId } = require("../../common/common");
const { isCorrectProperty } = require("./common");
const { doSplitMultipleValues } = require("../../common/sparql-common");

const baseUri = namespace.base;
// =======================================
// Generic
// =======================================
const _bindAddId = "bindAddId";
const _bindDelId = "bindDelId";
const _bindUpdId = "bindUpdId";
const _NewRandomId = "randomId";
// 定義特殊 property 以創建 instances
const _CreateInstance = "label";
// 資料放在 _Value 裡
const _Value = "value";
// member
const memberMapping = {
    hasEmployedAt: "hasEmployment",
    hasEducatedAt: "hasEducation",
    hasParticipant: "hasOrganization",
    hasFounded: "hasOrganization",
    wasConferredBy: "hasAward",
};

const isRandomId = (type) => {
    return instanceRandomID.find((cp) => cp.eventType.toLowerCase() === type.toLowerCase());
};

const isEventType = (type) => {
    return eventPrefix.find((cp) => cp.eventType.toLowerCase() === type.toLowerCase());
};

const isSupportType = (type) => {
    return classPrefix.find((cp) => cp.eventType.toLowerCase() === type.toLowerCase());
};

const getTypeFromPrefix = (srcId) => {
    return classPrefix.find((cp) => srcId.startsWith(cp.prefix));
};

const getPrefixFromType = (type) => {
    return classPrefix.find((cp) => type.toLowerCase() === cp.eventType.toLowerCase());
};

const getPropertyBetween = (srcType, dstType) => {
    return getApiProperty().find((ap) => ap.domain === srcType && ap.range === dstType);
};

// { invert: 'isFatherOf',
//   relationLabel: '有父',
//   invertLabel: '是...的父親',
//   relation: 'hasFather' },
const findRelation = (value) => {
    const relArr = getApiRelation();
    const found = relArr.find((rel) => {
        return rel.relation === value || rel.relationLabel === value;
    });
    return found ? found.relation : null;
};
const findInverseRelation = (value) => {
    const found = getApiRelation().find((rel) => {
        return rel.relation === value || rel.invert === value;
    });
    return found ? (found.invert === value ? found.relation : found.invert) : null;
};

const getPrefixByDomain = (predicate, apiProperty) => {
    // FamilyRelation or Relation
    const fPredicate = apiProperty.find((ap) => ap.property === predicate);
    if (fPredicate) {
        return classPrefix.find((cp) => cp.eventType === fPredicate.domain);
    } else {
        return { prefix: "", eventType: "" };
    }
};
const getPrefixByRange = (predicate, apiProperty) => {
    const fPredicate = apiProperty.find((ap) => ap.property === predicate);
    return classPrefix.find((cp) => cp.eventType === fPredicate.range);
};

const myProperty = (entry, bindId, isAdd) => {
    let qryStr = "";

    //{ graph: 'hkwrpr',
    //   classType: 'relationevent',
    //   value: { hasAdvisor: [ 'PER丁仁長' ] },
    //   srcId: 'PER金庸' }
    const { classType, value, srcId } = entry;

    // 先處理 Event 類型的資料
    const typeObj = isEventType(classType);

    if (typeObj) {
        //
        if (typeObj.eventType === RelationEventType) {
            const unUpdatedProp = [];
            let recordEventId;

            // RelationEvent 的建立方式
            // hasAdvisor: [ 'PER丁仁長', 'PER十三妹' ]
            Object.keys(value).forEach((evtKey) => {
                //
                const evtValue = value[evtKey];

                // 找出關係，有可能為 Array
                if (Array.isArray(evtValue)) {
                    evtValue.forEach((eval) => {
                        const { qryStr: tmpQryStr, eventId } = createRelationEvent(
                            srcId,
                            evtKey,
                            eval
                        );
                        qryStr += tmpQryStr;
                        if (eventId) {
                            recordEventId = eventId;
                        } else {
                            unUpdatedProp.push({ [evtKey]: eval });
                        }
                    });
                } else {
                    const { qryStr: tmpQryStr, eventId } = createRelationEvent(
                        srcId,
                        evtKey,
                        evtValue
                    );
                    qryStr += tmpQryStr;
                    if (eventId) {
                        recordEventId = eventId;
                    } else {
                        unUpdatedProp.push({ [evtKey]: evtValue });
                    }
                }
            });
            if (recordEventId) {
                unUpdatedProp.forEach((unUpdateditem) => {
                    qryStr += createNewData(recordEventId, unUpdateditem);
                });
            } else {
                unUpdatedProp.forEach((unUpdateditem) => {
                    qryStr += createNewData(bindId, unUpdateditem);
                });
            }
        } else {
            // default 的 event 建立方式
            const eventId = `?${_NewRandomId}`;
            //
            qryStr += createSubject2Event(eventId, srcId, typeObj);
            //
            qryStr += createNewData(eventId, value, isAdd);
        }
        return qryStr;
    }
    // 處理 data property 的類型
    qryStr += createNewData(bindId, value, isAdd);

    return qryStr;
};

exports.createObj = (entry) => {
    const resObj = { insert: "", remove: "", where: "" };

    const { srcId: label, classType } = entry;

    // property 及其值，放在 value 裡
    if (!entry.hasOwnProperty(_Value)) {
        console.log("ERROR::", _Value, " is required!");
        return resObj;
    }

    // #DaoYi#, 20220914, Vincent, 先處理 NameNode
    // NameNodeId 以 localNameId 當做 instance 的值
    // if (NameNodeType.toLowerCase() === classType.toLowerCase()) {
    //     resObj.insert += `?${_NewRandomId} a ${baseIri(NameNodeType)} .`;
    //     resObj.insert += myProperty(entry, `?${_NewRandomId}`, true);
    //     if (Object.keys(entry[_Value]).indexOf(localNameId) > -1) {
    //         // localNameId 為 id
    //         resObj.where += `BIND(IRI(CONCAT("${baseUri}", "NNI${entry[_Value][localNameId]}")) AS ?${_NewRandomId}) .`;
    //     } else {
    //         // random id
    //         resObj.where += `BIND(REPLACE(STR(BNODE()), ":", "") AS ?bnodeStr) .`;
    //         resObj.where += `BIND(IRI(CONCAT("${baseUri}", "NNI_", ?bnodeStr)) AS ?${_NewRandomId}) .`;
    //     }
    //     return resObj;
    // }

    const foundLabel = Object.keys(entry[_Value]).find((key) =>
        key.toLowerCase().startsWith("label_")
    );
    const foundBestKnownName = Object.keys(entry[_Value]).find((key) =>
        key.toLowerCase().startsWith("bestKnownName".toLowerCase())
    );

    if (entry[_Value].hasOwnProperty(_CreateInstance) || foundLabel || foundBestKnownName) {
        // 取出 label 的值
        let createInsLabel = entry[_Value][foundLabel || _CreateInstance];
        let createInsBestKnownName =
            (Array.isArray(entry[_Value][foundBestKnownName]) &&
                entry[_Value][foundBestKnownName]?.[0]) ||
            entry[_Value][foundBestKnownName];

        // 如果選擇已存在的 instance，則 srcId 的 prefix 符合 classType 的定義
        const foundPrefix = getTypeFromPrefix(label);
        if (foundPrefix && foundPrefix.eventType.toLowerCase() === classType.toLowerCase()) {
            // 產生完整的 iri
            const srcId = baseIri(replIllegalIRIChar(label));

            resObj.insert += `?${_bindAddId} a ${baseIri(foundPrefix.eventType)} .`;
            // 有值才存
            if (createInsLabel && createInsLabel.length > 0) {
                resObj.insert += doSplitMultipleValues(
                    createInsLabel,
                    "rdfs:label",
                    `?${_bindAddId}`
                );
                // resObj.insert += createInsLabel ? `?${_bindAddId} rdfs:label '''${createInsLabel}''' .` : "";
            }
            resObj.insert += myProperty(entry, `?${_bindAddId}`, true);
            resObj.where += `BIND(IRI(${srcId}) AS ?${_bindAddId}) .`;

            return resObj;
        }

        // 利用 BNODE() 隨機產生一個不重複的 id
        const randObj = isRandomId(classType);

        // 創建新的 instance
        if (randObj) {
            // 1. instance 為 random id
            resObj.where += `BIND(REPLACE(STR(BNODE()), ":", "") AS ?bnodeStr) .`;
            resObj.where += `BIND(IRI(CONCAT("${baseUri}", "${randObj.prefix}_", ?bnodeStr)) AS ?${_NewRandomId}) .`;
            resObj.insert += `?${_NewRandomId} a ${baseIri(randObj.eventType)} .`;
            resObj.insert += `?${_NewRandomId} rdfs:label '''${createInsLabel}''' .`;
            // Event 類別不需要塞 rdfs:label
            // if (!isEventType(classType)) {
            //     resObj.insert += `?${_NewRandomId} rdfs:label '''${createInsLabel}''' .`;
            // }
            resObj.insert += myProperty(entry, `?${_NewRandomId}`, true);
        } else {
            // 2. instance 與 label 相關
            // console.log("2. instance 與 label 相關");
            const supportType = isSupportType(classType);
            if (supportType) {
                const srcId = baseIri(
                    `${supportType.prefix}${replIllegalIRIChar(
                        createInsLabel || createInsBestKnownName
                    )}`
                );
                resObj.where += `BIND(IRI(${srcId}) AS ?${_bindAddId}) .`;
                resObj.insert += `?${_bindAddId} a ${baseIri(supportType.eventType)} .`;
                resObj.insert += createInsLabel
                    ? `?${_bindAddId} rdfs:label '''${createInsLabel}''' .`
                    : "";
                resObj.insert += myProperty(entry, `?${_bindAddId}`, true);
            } else {
                /////////////////////
                //  virtual graph  //
                /////////////////////d
                //
                /** example
                 INSERT {
                    GRAPH <http://hkbdb.lib.cuhk.edu.hk/v1/abcwhkp> {
                        <http://hkbdb.lib.cuhk.edu.hk/v1#PER徐雲鶴> <http://hkbdb.lib.cuhk.edu.hk/v1#hasAward> ?randomId1 .
                        ?randomId1 <http://hkbdb.lib.cuhk.edu.hk/v1#wasConferredBy> <http://hkbdb.lib.cuhk.edu.hk/v1#ORG國民大學> .
                        <http://hkbdb.lib.cuhk.edu.hk/v1#PER徐雲鶴> <http://hkbdb.lib.cuhk.edu.hk/v1#hasEducation> ?randomId2 .
                        ?randomId2 <http://hkbdb.lib.cuhk.edu.hk/v1#hasEducatedAt> <http://hkbdb.lib.cuhk.edu.hk/v1#ORG國民大學> .
                        <http://hkbdb.lib.cuhk.edu.hk/v1#PER徐雲鶴> <http://hkbdb.lib.cuhk.edu.hk/v1#hasEmployment> ?randomId3 .
                        ?randomId3 <http://hkbdb.lib.cuhk.edu.hk/v1#hasEmployedAt> <http://hkbdb.lib.cuhk.edu.hk/v1#ORG國民大學> .
                        <http://hkbdb.lib.cuhk.edu.hk/v1#PER徐雲鶴> <http://hkbdb.lib.cuhk.edu.hk/v1#hasOrganization> ?randomId4 .
                        ?randomId4 <http://hkbdb.lib.cuhk.edu.hk/v1#hasParticipant> <http://hkbdb.lib.cuhk.edu.hk/v1#ORG國民大學> .
                    }
                 }
                 WHERE {
                    BIND(REPLACE(STR(BNODE()), ":", "") AS ?bnodeStr) .
                    BIND(IRI(CONCAT("http://hkbdb.lib.cuhk.edu.hk/v1#", "AWEEVT_", ?bnodeStr)) AS ?randomId1) .
                    BIND(IRI(CONCAT("http://hkbdb.lib.cuhk.edu.hk/v1#", "EDUEVT_", ?bnodeStr)) AS ?randomId2) .
                    BIND(IRI(CONCAT("http://hkbdb.lib.cuhk.edu.hk/v1#", "EMPEVT_", ?bnodeStr)) AS ?randomId3) .
                    BIND(IRI(CONCAT("http://hkbdb.lib.cuhk.edu.hk/v1#", "ORGEVT_", ?bnodeStr)) AS ?randomId4) .
                }
                 */
                if (classType === "member") {
                    //
                    const personProp = Object.keys(entry.value).find((property) =>
                        property.startsWith("bestKnownName")
                    );
                    //
                    resObj.where += `BIND(REPLACE(STR(BNODE()), ":", "") AS ?bnodeStr) .`;
                    //
                    entry.value[personProp].map((personId, perIdx) => {
                        //
                        Object.keys(entry.value).forEach((property, propIdx) => {
                            //
                            if (!property.startsWith("label")) {
                                // split e.g. hasEducatedAt__Organization
                                const [valuePropName, valuePropRange] = property.split("__");
                                //
                                resObj.where += `BIND(IRI(CONCAT("${baseUri}", "MEMEVT_", ?bnodeStr)) AS ?${_NewRandomId}${perIdx}${propIdx}) .`;
                                //
                                if (memberMapping[valuePropName]) {
                                    resObj.insert += `${baseIri(personId)} ${baseIri(
                                        memberMapping[valuePropName]
                                    )} ?${_NewRandomId}${perIdx}${propIdx} .`;
                                }
                                //
                                const { value, ...restEntry } = entry;
                                const newEntry = {
                                    ...restEntry,
                                    value: { [property]: entry.value[property] },
                                };
                                //
                                // other property
                                const otherProp = myProperty(
                                    newEntry,
                                    `?${_NewRandomId}${perIdx}${propIdx}`,
                                    true
                                );
                                resObj.insert += otherProp;
                            }
                        });
                    });
                    //
                    return resObj;
                } else {
                    console.error(classType, " is not supported!", entry);
                    return resObj;
                }
            }
        }
    } else {
        // Event 類別
        const typeObj = isEventType(classType);

        // RelationEvent
        const { eventType: eventId } = getTypeFromPrefix(label);
        if (eventId === RelationEventType) {
            const srcId = baseIri(replIllegalIRIChar(label));
            resObj.where += `BIND(IRI(${srcId}) AS ?${_bindAddId}) .`;
            resObj.insert += myProperty(entry, `?${_bindAddId}`, true);
        }

        if (typeObj) {
            resObj.where += `BIND(REPLACE(STR(BNODE()), ":", "") AS ?bnodeStr) .`;
            resObj.where += `BIND(IRI(CONCAT("${baseUri}", "${typeObj.prefix}_", ?bnodeStr)) AS ?${_NewRandomId}) .`;
            resObj.insert += myProperty(entry, `?${_NewRandomId}`, true);
        } else {
            const srcId = baseIri(replIllegalIRIChar(label));
            resObj.where += `BIND(IRI(${srcId}) AS ?${_bindAddId}) .`;
            resObj.insert += myProperty(entry, `?${_bindAddId}`, true);
        }
    }

    return resObj;
};

exports.personObj = (entry) => {
    const { srcId: label } = entry;
    return baseIri(label);
};

const getMemberProperty = (srcId, dstId, values) => {
    //
    let foundTarget = Object.keys(values).find((propName) => propName.startsWith("bestKnownName"));
    //
    let template = [];
    //
    if (foundTarget) {
        //
        let foundPrefix;
        //
        values[foundTarget].forEach((value) => {
            //
            foundTarget = `${value}`;
            foundPrefix = classPrefix.find((item) => foundTarget.startsWith(item.prefix));
            foundTarget = foundPrefix ? foundTarget : `PER${foundTarget}`;
            foundTarget = baseIri(replIllegalIRIChar(foundTarget));
            //
            // example:
            // hkbdb:PER徐雲鶴 ?class hkbdb:AWEEVT_c3652480_34f5_4d46_9d11_ddf96a1aac61_136 .
            // hkbdb:AWEEVT_c3652480_34f5_4d46_9d11_ddf96a1aac61_136 ?class2 hkbdb:ORG愛荷華大學
            //
            template = template.concat([
                `${foundTarget} ?class ${srcId} .`,
                `${srcId} ?class2 ${dstId} .`,
            ]);
        });
    } else {
        const memberProps = Object.keys(memberMapping);
        const oriProps = Object.keys(values).map((propName) => propName.split("__")[0]);
        const isMemberProp = oriProps.find((prop) => memberProps.includes(prop));
        //
        if (isMemberProp) {
            template = template.concat([`?perId ?class ${srcId} .`, `${srcId} ?class2 ${dstId} .`]);
        }
    }
    //
    return template.join("\n\t");
};

exports.deleteObj = (entry) => {
    //
    const resObj = { insert: "", remove: "", where: "" };
    //
    const { srcId: tmpSrcId, dstId: tmpDstId, classType, value } = entry;
    //
    const srcId = baseIri(replIllegalIRIChar(tmpSrcId));
    const dstId = baseIri(replIllegalIRIChar(tmpDstId));
    //
    // 這段 if 只支援 member 這個虛擬 graph，其他都走正常流程
    if (classType === "member" && tmpDstId) {
        //
        const template = getMemberProperty(srcId, dstId, value);
        if (template) {
            resObj.remove += template;
            resObj.where += template;
            return resObj;
        }
    }
    //
    let bindId = _bindDelId;
    // Event 類型的資料
    const typeObj = isEventType(classType);
    if (typeObj) {
        bindId = _NewRandomId;
    }
    //
    resObj.where += `BIND(IRI(${srcId}) AS ?${bindId}) .`;
    resObj.remove += myProperty(entry, `?${bindId}`, false);
    //
    return resObj;
};

exports.deleteEventObj = (entry) => {
    const resObj = { insert: "", remove: "", where: "" };
    const { srcId: tmpSrcId } = entry;

    if (!tmpSrcId || tmpSrcId === "") {
        return resObj;
    }
    const srcId = baseIri(replIllegalIRIChar(tmpSrcId));

    resObj.remove += `?s ?p ?o .`;
    resObj.where += `
        {
            BIND(${srcId} AS ?s) .
            ?s ?p ?o .
        }
        UNION
        {
            BIND(${srcId} AS ?o) .
            ?s ?p ?o .
        }`;

    return resObj;
};

exports.updateObj = (entrySrc, entryDst) => {
    //
    const resObj = { insert: "", where: "", remove: "" };
    // 以 entrySrc 的 srcId, classType 為主
    const { srcId: label, classType } = entrySrc;
    //
    // 這段 if 只支援 member 這個虛擬 graph，其他都走正常流程
    if (classType === "member") {
        ///////////////////
        // remove, where //
        ///////////////////
        let {
            srcId: entrySrc_tmpSrcId,
            dstId: entrySrc_tmpDstId,
            value: entrySrc_value,
        } = entrySrc;
        //
        let srcId = baseIri(replIllegalIRIChar(entrySrc_tmpSrcId));
        let dstId = baseIri(replIllegalIRIChar(entrySrc_tmpDstId));
        //
        // 這段 if 只支援 member 這個虛擬 graph，其他都走正常流程
        if (dstId) {
            const template = getMemberProperty(srcId, dstId, entrySrc_value);
            if (template) {
                // resObj.insert += template;
                resObj.remove += template;
                resObj.where += template;
            }
        }
        ////////////
        // insert //
        ////////////
        let {
            srcId: entryDst_tmpSrcId,
            dstId: entryDst_tmpDstId,
            value: entryDst_value,
        } = entryDst;
        //
        srcId = baseIri(replIllegalIRIChar(entryDst_tmpSrcId));
        dstId = baseIri(replIllegalIRIChar(entryDst_tmpDstId));
        //
        // 這段 if 只支援 member 這個虛擬 graph，其他都走正常流程
        if (dstId) {
            const template = getMemberProperty(srcId, dstId, entryDst_value);
            if (template) {
                resObj.insert += template;
                if (isEmpty(resObj.remove)) {
                    resObj.where += getMemberProperty(srcId, dstId, {});
                }
            }
        }
        // return resObj;
    }
    //
    const srcId = baseIri(replIllegalIRIChar(label));
    //
    entryDst.srcId = label;
    entryDst.classType = classType;
    // no change
    if (isObjectEqual(entrySrc, entryDst)) {
        return resObj;
    }
    //
    let bindId = _bindUpdId;
    // Event 類型的資料, 使用 _NewRandomId
    const typeObj = isEventType(classType);
    //
    if (typeObj) {
        bindId = _NewRandomId;
    }
    //
    // Update 必定會有 srcId
    resObj.where += `BIND(IRI(${srcId}) AS ?${bindId}) .`;
    // delete old
    resObj.remove += myProperty(entrySrc, `?${bindId}`, false);
    // create new
    resObj.insert += myProperty(entryDst, `?${bindId}`, true);
    //
    return resObj;
};

const createPropertyValue = (bindId, opObj, value, range, isAdd) => {
    const { prefix } = getPrefixFromType(range);

    let sObj = "";

    // DateEvent
    if (DateEventType === range) {
        // 新增 DateEvent 的同時要產生 year, month, day
        // 刪除 DateEvent 的時候，只要刪除與 DateEvent 的連結即可
        if (isAdd) {
            sObj = createDateEvent(bindId, opObj, value, range, prefix);
        } else {
            sObj = deleteDateEvent(bindId, opObj, value, prefix);
        }
        return sObj;
    }

    // 為變數形態，直接 assgin
    // 如：?randomId_EducationEvent
    if (value.startsWith("?")) {
        return `${bindId} ${opObj} ${value} .`;
    }

    // 接受帶過來的參數有 prefix 或沒有
    // PER高雄 or 高雄
    if (value.startsWith(prefix)) {
        sObj = baseIri(replIllegalIRIChar(value));
    } else {
        sObj = baseIri(replIllegalIRIChar(`${prefix}${value}`));
    }
    return `${bindId} ${opObj} ${sObj} .`;
};

function createNewData(bindId, valueObj, isAdd) {
    const apiProperty = getApiProperty();

    let qryStr = "";

    Object.keys(valueObj).forEach((prop) => {
        let value = valueObj[prop];

        const [propName, propRange] = prop.split("__");

        // find detail property information
        let foundp = apiProperty.find((ap) => ap.property === propName);

        const isLabelProperty = `${propName}`.toLowerCase().startsWith("label_");

        if (!foundp) {
            if (isLabelProperty) {
                const [label, domain] = propName.split("_");
                foundp = {
                    domain: domain,
                    property: label,
                    range: propRange,
                    type: "data",
                };
            }
        }

        if (!foundp) {
            console.error("the detail of the property can't be found by the property:", propName);
            return;
        }

        //{ domain: 'Person',
        //  property: 'isInFamily',
        //  range: 'string',
        //  type: 'data' }
        const { property, range, type } = foundp;

        let newRange;

        // 如果 value's range 和 foundp's range 不同，則以 value 的 range 為準
        // data property 只會有一個 range，以 protege 的 range 為主
        if (range === propRange || type === "data") {
            newRange = range;
        } else {
            newRange = propRange;
        }

        // DateEvent's value 不需要有 prefix
        if (newRange === "DateEvent") {
            if (Array.isArray(value)) {
                value = [...value.map((v) => v.replace("DAE_", ""))];
            } else {
                value = [value.replace("DAE_", "")];
            }
        }

        let opObj;
        if (isLabelProperty) {
            //
            opObj = rdfsIri(property);
            //
            if (!Array.isArray(value)) {
                value = [value];
            }
            //
            // rdfs:label's value 不需要有 prefix
            let newValue = value.map((v) => {
                let foundPrefix = classPrefix.find((item) => v.startsWith(item.prefix));
                if (foundPrefix) {
                    return v.replace(foundPrefix.prefix, "");
                }
                return v;
            });
            value = [...newValue];
        } else {
            opObj = baseIri(property);
        }

        if (type === "data") {
            if (newRange === "float") {
                qryStr += `${bindId} ${opObj} '''${value}'''^^xsd:float .`;
            } else if (newRange === "integer") {
                qryStr += `${bindId} ${opObj} '''${value}'''^^xsd:integer .`;
            } else if (newRange === "double") {
                qryStr += `${bindId} ${opObj} '''${value}'''^^xsd:double .`;
            } else {
                qryStr += doSplitMultipleValues(value, opObj, bindId);
            }
        } else if (type === "object") {
            // create new instances
            if (Array.isArray(value)) {
                value.forEach((eval) => {
                    qryStr += createPropertyValue(bindId, opObj, eval, newRange, isAdd);

                    // event 的反關係
                    // if (eventObj && val !== "") {
                    // 20221214, Vincent: 不一定 event 才有反關係, 需要全部處理.
                    if (eval !== "") {
                        // 如果 property 為 event，需要多設定 inverse 關係
                        const inverseOpObj = findInverseRelation(propName);

                        if (!inverseOpObj || inverseOpObj === "") {
                            // 無需增加反關係
                            return;
                        }

                        // 極特殊情況，inverse 關係並不屬於此 Class
                        // 如：Person hasSpecialty SpecialtyEvent
                        //    SpecialtyEvent hasSpecialty Specialty
                        // 但是 Specialty 並沒有 inverse 關係 SpecialtyEvent
                        // 以 hasSpecialty 皆可找到 inverse 關係 isSpecialtyOf
                        if (!isCorrectProperty(newRange, inverseOpObj)) {
                            return;
                        }

                        const iriInvOpObj = baseIri(inverseOpObj);
                        const iriVal = baseIri(eval);

                        // Create 的時候宣告 ClassType, 但是刪除時不能刪
                        if (isAdd) {
                            qryStr += `${iriVal} a ${baseIri(newRange)} .`;
                        }
                        qryStr += createPropertyValue(iriVal, iriInvOpObj, bindId, newRange, isAdd);
                    }
                });
            } else {
                qryStr += createPropertyValue(bindId, opObj, value, newRange, isAdd);

                // event 的反關係
                // if (eventObj && val !== "") {
                // 20221214, Vincent: 不一定 event 才有反關係, 需要全部處理.
                if (value !== "") {
                    // 如果 property 為 event，需要多設定 inverse 關係
                    const inverseOpObj = findInverseRelation(propName);

                    if (!inverseOpObj || inverseOpObj === "") {
                        // 無需增加反關係
                        return;
                    }

                    // 極特殊情況，inverse 關係並不屬於此 Class
                    // 如：Person hasSpecialty SpecialtyEvent
                    //    SpecialtyEvent hasSpecialty Specialty
                    // 但是 Specialty 並沒有 inverse 關係 SpecialtyEvent
                    // 以 hasSpecialty 皆可找到 inverse 關係 isSpecialtyOf
                    if (!isCorrectProperty(newRange, inverseOpObj)) {
                        return;
                    }

                    const iriInvOpObj = baseIri(inverseOpObj);
                    const iriVal = baseIri(value);

                    // Create 的時候宣告 ClassType, 但是刪除時不能刪
                    if (isAdd) {
                        qryStr += `${iriVal} a ${baseIri(newRange)} .`;
                    }
                    qryStr += createPropertyValue(iriVal, iriInvOpObj, bindId, newRange, isAdd);
                }
            }
        }
    });
    return qryStr;
}

function createRelationEvent(subject, evtKey, evtValue) {
    // init
    let qryStr = "";

    // loading property
    const apiProperty = getApiProperty();

    // split evtPropKey
    const evtKeyArr = evtKey.split("__");
    const [evtPropName, evtPropRange] = evtKeyArr;

    // 找出反關係
    const predicate = findRelation(evtPropName);
    const inverse = findInverseRelation(evtPropName);

    if (!predicate || !inverse) {
        console.log("can't find relation by :", evtPropName, evtValue);
        return { qryStr, eventId: "" };
    }

    // check isPerPrefix (人際關係 A and B 都是人物)
    let newSubject = replIllegalIRIChar(subject);
    let newEvtValue = replIllegalIRIChar(evtValue);

    // To check the prefix has a `PER` (人際關係 A and B 都是人物)
    if (!`${newSubject}`.startsWith("PER")) {
        newSubject = `PER${newSubject}`;
    }
    if (!`${newEvtValue}`.startsWith("PER")) {
        newEvtValue = `PER${newEvtValue}`;
    }

    const subId = baseIri(replIllegalIRIChar(newSubject));
    const objId = baseIri(replIllegalIRIChar(newEvtValue));

    const { prefix, eventType } = getPrefixByDomain(predicate, apiProperty);

    const eventId = baseIri(`${prefix}_${newSubject}_${predicate}_${newEvtValue}`);

    // 取得 relation property: hasRelation
    const srcType = getTypeFromPrefix(newSubject);
    const eventOp = getPropertyBetween(srcType.eventType, eventType);

    if (!eventOp) {
        console.error("createRelationEvent ERROR::", srcType, eventType);
        return { qryStr, eventId };
    }
    const eventPredicate = baseIri(findRelation(eventOp.property));

    // 正關係
    qryStr += `${subId} ${eventPredicate} ${eventId} .`;
    qryStr += `${eventId} a ${baseIri(eventType)} .`;
    qryStr += `${eventId} ${baseIri(predicate)} ${objId} .`;

    // 反關係
    const eventInvId = baseIri(`${prefix}_${newEvtValue}_${inverse}_${newSubject}`);
    qryStr += `${objId} ${eventPredicate} ${eventInvId} .`;
    qryStr += `${eventInvId} a ${baseIri(eventType)} .`;
    qryStr += `${eventInvId} ${baseIri(inverse)} ${subId} .`;

    return { qryStr, eventId };
}

function createSubject2Event(eventId, srcId, dstType) {
    const srcType = getTypeFromPrefix(srcId);

    const eventOp = getPropertyBetween(srcType.eventType, dstType.eventType);

    if (!eventOp) {
        console.error("createSubject2Event ERROR::", srcType, dstType);
        return "";
    }

    let qryStr = "";
    const subId = baseIri(replIllegalIRIChar(srcId));

    // 找出反關係
    const predicate = findRelation(eventOp.property);
    const inverse = findInverseRelation(eventOp.property);

    if (predicate) {
        qryStr += `${subId} ${baseIri(predicate)} ${eventId} .`;
    }
    // 可能事件只有正關係，而沒有反關係
    if (inverse) {
        qryStr += `${eventId} ${baseIri(inverse)} ${subId} .`;
    }
    qryStr += `${eventId} a ${baseIri(dstType.eventType)} .`;

    return qryStr;
}
