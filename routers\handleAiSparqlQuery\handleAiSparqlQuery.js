const {
    RESPONSE_OK,
    RESPONSE_BAD_REQUEST,
    RESPONSE_UNAUTHORIZED,
    ERROR_GENERATE_SPARQL,
    ERROR_INVALID_TOKEN,
    ERROR_MISSING_TEXT,
} = require("../../config/config");
const axios = require("axios");

const UAT_TOKEN = "uat-secret-token-from-cuhk";

// API 配置
const API_CONFIG = {
    ENDPOINT: 'https://cuhk-ai-uat.mobinology.com/api/text-to-sparql',
    TIMEOUT: 30000, // 30 seconds
};

const validateToken = (authHeader) => {
    if (!authHeader) {
        return false;
    }
    const token = authHeader.split(" ")[1];
    return token === UAT_TOKEN;
};


const callGenerateSparqlAPI = async (text, authToken) => {
    try {
        const response = await axios({
            method: 'post',
            url: API_CONFIG.ENDPOINT,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': authToken
            },
            data: {
                text: text
            },
            timeout: API_CONFIG.TIMEOUT
        });

        if (response.status === 200 && response.data) {
            return {
                success: true,
                data: response.data
            };
        }

        return {
            success: false,
            error: 'Invalid API response'
        };

    } catch (error) {
        console.error('API call error:', error);
        return {
            success: false,
            error: error.response?.data?.error || 'API call failed'
        };
    }
};


const handleAiSparqlQuery = async (req, res) => {
    // Validate auth token
    const authHeader = req.headers.authorization;
    if (!validateToken(authHeader)) {
        return res.status(RESPONSE_OK).json({
            status: 401,
            sparql: null,
            error: { error: ERROR_INVALID_TOKEN },
        });
    }

    const { text } = req.body;

    // Validate input
    if (!text || !text.trim()) {
        return res.status(RESPONSE_OK).json({
            status: 400,
            sparql: null,
            error: { error: ERROR_MISSING_TEXT },
        });
    }

    try {
        // 測試模式：如果text包含特定字串，觸發對應錯誤
        // if (text.includes("TEST_401")) {
        //     return res.status(RESPONSE_OK).json({
        //         status: 401,
        //         sparql: null,
        //         error: { error: ERROR_INVALID_TOKEN },
        //     });
        // }

        // if (text.includes("TEST_400")) {
        //     return res.status(RESPONSE_OK).json({
        //         status: 400,
        //         sparql: null,
        //         error: { error: ERROR_MISSING_TEXT },
        //     });
        // }

        const apiResponse = await callGenerateSparqlAPI(text, authHeader);
        if (!apiResponse.success) {
            return res.status(RESPONSE_OK).json({
                status: 400,
                sparql: null,
                error: {
                    error: apiResponse.error
                }
            });
        }

        return res.status(RESPONSE_OK).json({
            status: 200,
            sparql: apiResponse.data.sparql,
            error: null
        });


    } catch (error) {
        console.error("Error generating SPARQL:", error);
        return res.status(RESPONSE_OK).json({
            status: 500,
            sparql: null,
            error: { error: ERROR_GENERATE_SPARQL },
        });
    }
};

module.exports = handleAiSparqlQuery;
