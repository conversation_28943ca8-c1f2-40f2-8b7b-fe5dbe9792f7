// firebase
const firebase = require("../../../config/config.firebase");
const rtdb = firebase.database();
const firebaseHelper = {};
const commonHelper = require("./commonHelper");

firebaseHelper.rtDbGet = (path) => {
    const data = {};
    return rtdb
        .ref(path)
        .once("value")
        .then((snapshot) => {
            const snapVal = snapshot.val() || {};
            if (typeof snapVal === "object") {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
                return data;
            }
            if (typeof snapshot.val() === "string") {
                throw new Error("firebase data not valid");
            }
        })
        .catch((err) => {
            return err;
        });
};

firebaseHelper.rtDbUpdate = (path, updateVal) => {
    if (!(path && updateVal)) return null;
    return firebase
        .database()
        .ref(path)
        .update(updateVal)
        .then((res) => {
            return res;
        })
        .catch((err) => {
            return err;
        });
};

// 更新 realtime database 的資訊: files 新增 file 資訊, register 新增 file 及 user 資訊
firebaseHelper.genRtDbSnapValRegister = ({
    data = {},
    user,
    fileName,
    fileKey,
    pattern,
}) => {
    const ts = Date.now();
    const { name, email, uid: userUid } = user || {};
    const patternStr = commonHelper.safeGetFirstEle(pattern);
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                pattern: patternStr,
                requestTime: new Date(ts),
                fileName: fileName,
                createdTime: null,
                downloadUrl: null,
                requestUser: name,
                requestEmail: email,
            },
        },
        register: {
            ...(data.register || {}),
            [fileKey]: {
                [userUid]: {
                    email: email,
                    name: name,
                    emailRecord: {},
                    downloadRecord: {},
                },
            },
        },
    };
};

firebaseHelper.getRtDbUpdate4DownloadFail = ({
    data = {},
    fileKey,
    errorMessage,
}) => {
    const ts = Date.now();
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                ...(data || {}).files[fileKey],
                error: "true",
                errorTime: new Date(ts),
                errorMessage: errorMessage,
            },
        },
    };
};

firebaseHelper.getRtDbUpdate4DownloadReady = ({
    data = {},
    user,
    fileKey,
    downloadUrl,
}) => {
    const ts = Date.now();
    const { name, email, uid: userUid } = user || {};
    return {
        ...(data || {}),
        files: {
            ...((data || {}).files || {}),
            [fileKey]: {
                ...(data || {}).files[fileKey],
                finishTime: new Date(ts),
                downloadUrl: downloadUrl,
            },
        },
        register: {
            ...((data || {}).register || {}),
            [fileKey]: {
                [userUid]: {
                    email: email,
                    name: name,
                    emailRecord: {
                        [ts]: "true",
                    },
                    downloadRecord: {},
                },
            },
        },
    };
};

module.exports = firebaseHelper;
