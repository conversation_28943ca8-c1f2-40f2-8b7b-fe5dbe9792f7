const { doSaprqlUpdate } = require("../../../common/sparql-common");
const { baseGraph, namespace} = require("../../../sparql/rdf");
const {bs64DecodeQueryStr} = require("../../../common/common");
const {deleteObj, deleteEventObj} = require("../../../sparql/properties/generic");

const deleteEvent = (entry) => {
    const { graph } = entry;
    const { remove, where } = deleteEventObj(entry);

    const _queryStr = `
        DELETE {
            GRAPH <${namespace.graph}${graph}> {
                ${remove}
            }
        }
        WHERE {
            GRAPH <${namespace.graph}${graph}> {
                ${where}
            }
        }
    `;

    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
    // return new Promise((resolve) => resolve({ status: 200 }))
};

exports.deleteEvent20 = async (entry, callback) => {
    await Promise.all([deleteEvent(entry)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
