const { generic20 } = require("./lib/updateGeneric");
const { mergeInstances20 } = require("./lib/updateMergeInstances");
const { duplicateInstances20 } = require("./lib/updateDuplicateInstances");
const { moveInstances20 } = require("./lib/updateMoveInstances");
const { moveFromDraftInstances20 } = require("./lib/updateMoveFormDraftInstances");

const index = {
    generic: { "2.0": generic20, required: ["srcId"] },
    mergeInstances: { "2.0": mergeInstances20, required: ["srcId"] },
    duplicateInstances: { "2.0": duplicateInstances20, required: ["srcId"] },
    moveInstances: { "2.0": moveInstances20, required: ["value"] },
    moveFromDraftInstances: { "2.0": moveFromDraftInstances20, required: ["value"] },
};

module.exports = index;
