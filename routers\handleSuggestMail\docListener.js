const firebase = require("../../config/config.firebase");
const firestoreDb = firebase.firestore();
const docListener = (col, doc,callback) => {
    const colRef = firestoreDb.collection(col).doc(doc);

    colRef.onSnapshot(
        (docSnapshot) => {
            console.log(`Received doc snapshot: ${docSnapshot}`);
            const data = docSnapshot.data()
            callback(data);
        },
        (err) => {
            console.log(`Encountered error: ${err}`);
            callback(null);
        }
    );
};

module.exports = docListener;



