const express = require("express");
const app = express();
const logger = require("morgan");
const safeHeader = require("./routers/handleHeader");
const { customDebug } = require("./commons/debug");
const useEnv = require("./commons/useEnv");
const handleSuggestMail = require("./routers/handleSuggestMail");

// use .env.development or .env.production
useEnv();
// disable console in production
customDebug();
// handle suggestMail
handleSuggestMail();
// middleware of app
app.use(express.static("public"));
app.use(logger("dev"));
app.use(express.json({ limit: "50mb", extended: true }));
app.use(express.urlencoded({ limit: "50mb", extended: true }));
app.use(safeHeader);
// router
require("./routers/routerManager.js")(app);

const PORT = process.env.PORT || 4200;
app.listen(PORT, "127.0.0.1", () => {
    console.log(`Listen to port ${PORT}`);
});
