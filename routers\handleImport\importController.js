// config
const importConfig = require("../../config/config.import");
// helpers
const fileHelper = require("./helpers/fileHelper");
const errorHelper = require("./helpers/errorHelper");
const firebaseHelper = require("./helpers/firebaseHelper");
const importMain = require("./importMain");
//
const { getFilePath, getFileNameAndKey, fileValidate } = fileHelper;

const importController = {
    importFile: async (req, res, next) => {
        const { pattern } = req.params;
        const { userName, userEmail } = req.body;
        const user = { name: userName || "", email: userEmail || "" };

        // 取得檔案路徑
        const filePath = getFilePath(req);
        // 取得 fileKey and fileName
        const { fileKey, fileName } = getFileNameAndKey(req);
        // realtime database specific path
        // rtDbPath: "/database/import";
        const rtDbPath = importConfig.firebaseRtDb.readWritePath;

        try {
            // 先取得 realtime database 設定檔
            const rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
            // console.log("rtDbDataObj", rtDbDataObj);

            if (rtDbDataObj instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                );
            }

            let rtUpdateRes;
            if (rtDbDataObj) {
                const updateVal = firebaseHelper.genRtDbSnapValRegister({
                    data: rtDbDataObj,
                    pattern,
                    user,
                    fileName,
                    fileKey,
                });
                // console.log("updateVal", updateVal);
                // 更新 realtime database 的資訊
                rtUpdateRes = await firebaseHelper.rtDbUpdate(
                    rtDbPath,
                    updateVal
                );
            }

            if (rtUpdateRes instanceof Error) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                );
            }

            let patternSetting = importConfig.pattern[pattern];
            // 確認是否有該 import pattern
            if (!patternSetting) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.PATTERN_NOT_EXIST.name
                );
            }

            const isFileValidate = await fileValidate(req);
            // 確認要處理那個檔案是否存在
            if (!isFileValidate) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.UPLOAD_FAIL.name
                );
            }

            if (!(fileKey && fileName && filePath)) {
                errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.UPLOAD_FAIL.name
                );
            }

            // 確認 file 欄位符合規定
            const fileSheetRows = await fileHelper.readExcelFile(filePath);

            // 讀取檔案的 object 是否符合規定
            if (!(fileSheetRows && fileSheetRows[0] && fileSheetRows[0].data)) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.SERVER_READ_FILE_FAIL.name
                );
            }
            // 目前檔案的欄位
            const cols = fileHelper.getExcelCols(fileSheetRows[0].data);
            // 基本欄位
            const defaultCols = patternSetting.fileCols;
            const colsEqual = fileHelper.checkColsEqual(cols, defaultCols);

            // 檔案中的欄位不符合設定
            if (!colsEqual) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.FILE_COLS_ILLEGAL.name
                );
            }

            const rows =
                Array.isArray(fileSheetRows) &&
                fileSheetRows.length > 0 &&
                fileSheetRows[0].data;
            const mapList = fileHelper.arrayToMapList(rows.slice(1), rows[0]);

            let customValidate;
            // 其他檢查: 依據 pattern 檢查特定欄位
            if (pattern === importConfig.pattern.auda_hklit.name) {
                // 檢查 Best_Known_Name 欄位
                const checkCol = patternSetting.fileColsCheck.Best_Known_Name;
                customValidate = fileHelper.validateSingleColVal(
                    mapList,
                    checkCol,
                    fileHelper.bestKnownNameChecker
                );
            }
            if (!customValidate) {
                return errorHelper.sendErrorCode(
                    errorHelper.ERROR_CODE.BEST_KNOWN_NAME_ILLEGAL.name
                );
            }

            // 開始寫入流程
            importMain(req, fileSheetRows, fileKey);
            //
            return res.json({
                status: "success",
                message: "File upload will soon be processed",
            });
        } catch (err) {
            console.log("error:", err.message);
            let errObj = errorHelper.ERROR_CODE.DEFAULT;
            if (err.message in errorHelper.ERROR_CODE) {
                errObj = errorHelper.ERROR_CODE[err.message];
            }

            let rtDbDataObj, rtUpdateRes;
            try {
                // 先取得 realtime database 設定檔
                rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
                // console.log("rtDbDataObj", rtDbDataObj);

                if (rtDbDataObj instanceof Error) {
                    return errorHelper.sendErrorCode(
                        errorHelper.ERROR_CODE.FIREBASE_READ_ERROR.name
                    );
                }
                // firebase 更新
                if (rtDbDataObj) {
                    const updateVal = firebaseHelper.getRtDbUpdate4ImportFail({
                        data: rtDbDataObj,
                        fileKey,
                        errorMessage: errObj.message,
                    });
                    // 更新 realtime database 的資訊
                    rtUpdateRes = await firebaseHelper.rtDbUpdate(
                        rtDbPath,
                        updateVal
                    );
                }
                if (rtUpdateRes instanceof Error) {
                    return errorHelper.sendErrorCode(
                        errorHelper.ERROR_CODE.FIREBASE_UPDATE_ERROR.name
                    );
                }
                if (errObj) {
                    res.status(errObj.code).json({
                        statusCode: errObj.code,
                        message: errObj.message,
                    });
                }
            } catch (err) {
                res.status(errObj.code).json({
                    statusCode: errObj.code,
                    message: errObj.message,
                });
            }
        }
    },
};

module.exports = importController;
