const { namespace } = require("../../../sparql/rdf");
const { createObj } = require("../../../sparql/properties/generic");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const generic = async (entry) => {
    const { graph } = entry;
    const { insert, where } = createObj(entry);

    const _queryStr = `
            INSERT {
                GRAPH <${namespace.graph}${graph}> {
                    ${insert}
                }
            }
            WHERE {
                ${where}
            }
            `;

    // console.log("_queryStr", _queryStr);
    return doSaprqlUpdate(_queryStr);
    // return new Promise((resolve) => resolve({ status: 200 }))
};

exports.generic20 = async (entry, callback) => {
    await Promise.all([generic(entry)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
