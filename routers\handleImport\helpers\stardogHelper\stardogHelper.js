const {
    doSaprqlQuery,
} = require("../../../handleSparql/services/common/sparql-common");
const updateAudaHklit = require("./update/audaHklit/audaHklit");
const updatePublication = require("./update/publication/publication");
//
const importConfig = require("../../../../config/config.import");

const stardogHelper = {
    map: {
        [importConfig.pattern.auda_hklit.name]: "updateAudaHklit",
        // [importConfig.pattern.publications.name]: "updatePublication",
    },
    updateAudaHklit: updateAudaHklit,
    // updatePublication: updatePublication,
    updateStardog: async (queryString) => {
        try {
            console.log("updateStardog");
            console.log(queryString);
            const queryRes = await doSaprqlQuery(queryString);
            if (queryRes) {
                return "updateStardog finish";
            } else {
                return "updateStardog fail";
            }
        } catch (err) {
            return err;
        }
    },
};

module.exports = stardogHelper;
