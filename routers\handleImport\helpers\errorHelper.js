const ERROR_CODE = {
    DEFAULT: {
        name: "DEFAULT",
        code: 500,
        message: "ServerError",
    },
    FIREBASE_READ_ERROR: {
        name: "FIREBASE_READ_ERROR",
        code: 400,
        message: "FIREBASE_READ_ERROR",
    },
    FIREBASE_UPDATE_ERROR: {
        name: "FIREBASE_UPDATE_ERROR",
        code: 400,
        message: "FIREBASE_UPDATE_ERROR",
    },
    PATTERN_NOT_EXIST: {
        name: "PATTERN_NOT_EXIST",
        code: 400,
        message: "尚未支援該匯入類型",
    },
    UPLOAD_FAIL: {
        name: "UPLOAD_FAIL",
        code: 400,
        message: "Upload file fail",
    },
    SERVER_READ_FILE_FAIL: {
        name: "SERVER_READ_FILE_FAIL",
        code: 500,
        message: "伺服器讀取檔案失敗",
    },
    FILE_COLS_ILLEGAL: {
        name: "FILE_COLS_ILLEGAL",
        code: 400,
        message: "檔案中的欄位不符合設定, 請參考範本編寫你的檔案",
    },
    // 進入寫入流程
    FILE_ROWS_BLANK: {
        name: "FILE_ROWS_BLANK",
        code: 400,
        message: "檔案工作表中無資料",
    },
    DATABASE_BACKUP_ERROR: {
        name: "DATABASE_BACKUP_ERROR",
        code: 500,
        message: "備份資料庫失敗",
    },
    WRITE_DATABASE_ERROR: {
        name: "WRITE_DATABASE_ERROR",
        code: 500,
        message: "寫入資料庫失敗",
    },
    WRITE_FILE_ERROR: {
        name: "WRITE_FILE_ERROR",
        code: 500,
        message: "Write file failed in writeFile",
    },
    NO_PATTERN_WRITER_METHOD: {
        name: "NO_PATTERN_WRITER_METHOD",
        code: 500,
        message: "尚未定義該 pattern 的寫入資料庫方式",
    },
    BEST_KNOWN_NAME_ILLEGAL: {
        name: "BEST_KNOWN_NAME_ILLEGAL",
        code: 400,
        message: "Best_Known_Name 欄位格式不符, 欄位值必須包含 @PER 或 @ORG",
    },
    NAME_ID_NOT_MATCH_LOCAL_NAME_ID: {
        name: "NAME_ID_NOT_MATCH_LOCAL_NAME_ID",
        code: 400,
        message: "Name_ID not match with Local_Name_ID",
    },
    GET_NEXT_NAME_ID_ERROR: {
        name: "GET_NEXT_NAME_ID_ERROR",
        code: 500,
        message: "Fail to get next nameId",
    },
};

const errorHelper = {
    ERROR_CODE: ERROR_CODE,
    sendErrorCode: (errorKey) => {
        if (errorKey && errorKey in ERROR_CODE) {
            console.log(ERROR_CODE[errorKey].message);
        }

        throw new Error(errorKey);
    },
};

module.exports = errorHelper;
