const configFuseki = {};
configFuseki.port = 5833;
if (process.env.NODE_ENV === "production") {
    const {
        db,
        url,
        user,
        password,
        draftDB,
    } = require("/opt/private-config/hkbdb-fuseki-gptReview2.json");
    configFuseki.db = db;
    configFuseki.url = url;
    configFuseki.user = user;
    configFuseki.password = password;
    configFuseki.draftDB = draftDB;
} else {
    configFuseki.db = "gptReview";
    configFuseki.url = `http://localhost:${configFuseki.port}`;
    configFuseki.user = "admin";
    configFuseki.password = "admin";
}

console.log("[ENV] FUSEKI_GPTREVIEW_DB", configFuseki.db);
console.log("[ENV] FUSEKI_GPTREVIEW_URL", configFuseki.url);

module.exports = configFuseki;
