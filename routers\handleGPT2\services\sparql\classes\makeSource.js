const { namespace, baseIri } = require("../rdf");
const { isObjectEqual, convertToArray } = require("../../common/common");
const { doQueryNxtMaxId } = require("../../common/sparql-common");

// =======================================
// Source
// =======================================
const _bindId = "sourceId";
const _className = "Source";
const _classPrefix = "SOU";

const myProperty = (entry, parentId, subjectId) => {
    const { hasSource, externalLinks } = entry;

    let queryStr = "";
    if (hasSource) {
        queryStr += `${subjectId} rdfs:label '''${hasSource}''' .`;
    }
    if (externalLinks) {
        const resArr = convertToArray(externalLinks);
        resArr.forEach((link) => {
            queryStr += `${subjectId} ${baseIri(
                "externalLinks"
            )} '''${link}''' .`;
        });
    }

    // if values exist
    if (queryStr !== "") {
        queryStr += `?${parentId} ${baseIri("hasSource")} ${subjectId} .`;
        queryStr += `${subjectId} a ${baseIri(_className)} .`;
    }
    return queryStr;
};

exports.createSource = (entry, parentId) => {
    const { hasSource } = entry;

    const resObj = { insert: "", where: "", remove: "" };

    if (!hasSource) {
        return resObj;
    }
    resObj.insert += myProperty(entry, parentId, `?${_bindId}`);

    resObj.where += doQueryNxtMaxId(_className, _classPrefix, _bindId);
    return resObj;
};

exports.deleteSource = (entry) => {
    const { graph, hasSourceId } = entry;
    const pbioGraph = `<${namespace.graph}${graph}>`;

    const resObj = { insert: "", where: "", remove: "" };
    if (!hasSourceId) {
        return resObj;
    }

    const subjectId = `${baseIri(hasSourceId)}`;
    resObj.remove += `${subjectId} ?${hasSourceId}P ?${hasSourceId}O .`;
    resObj.where += `GRAPH ${pbioGraph} { ${subjectId} ?${hasSourceId}P ?${hasSourceId}O . }`;
    return resObj;
};

exports.updateSource = (entrySrc, entryDst, parentId) => {
    const resObj = { insert: "", where: "", remove: "" };

    // no change
    if (isObjectEqual(entrySrc, entryDst)) {
        return resObj;
    }

    // delete
    {
        const { remove, where } = this.deleteSource(entrySrc);
        resObj.remove += remove;
        resObj.where += where;
    }

    // create by previous id
    {
        const { hasSourceId } = entryDst;
        const subjectId = `?${_bindId}`;
        if (!hasSourceId) {
            const { insert, where } = this.createSource(entryDst, parentId);
            resObj.insert += insert;
            resObj.where += where;
        } else {
            resObj.insert += myProperty(entryDst, parentId, subjectId);
            resObj.where += `BIND(${baseIri(hasSourceId)} AS ?${_bindId}) .`;
        }
    }

    return resObj;
};
