const { namespace } = require("../../../sparql/rdf");
const { deleteObj } = require("../../../sparql/properties/generic");
const { doSaprqlUpdate } = require("../../../common/sparql-common");

const generic = async (entry) => {
    const { graph } = entry;
    const { remove, where } = deleteObj(entry);

    const _queryStr = `
DELETE {
    GRAPH <${namespace.graph}${graph}> {
        ${remove}
    }
}
WHERE {
    ${where}
}
    `;

    // console.log(_queryStr);
    return doSaprqlUpdate(_queryStr);
    // return new Promise((resolve) => resolve({ status: 200 }))
};

exports.generic20 = async (entry, callback) => {
    await Promise.all([generic(entry)])
        .then(
            (values) => {
                callback(values, null);
            },
            (reason) => {
                callback(null, reason);
            }
        )
        .catch((error) => {
            callback(null, error);
        });
};
