const { decodePersonId, decodeId } = require("./services/common/common");

const { verifyToken } = require("../handleAuth/auth");
const deleteApiList = require("./services/crud/deleteApis");
const {
    ERROR_NO_PARAM_VER,
    ERROR_API_NOT_EXIST,
    ERROR_API_METHOD,
    ERROR_LACK_OF_PARAMS,
    ERROR_API_VER_NOT_EXIST,
    ERROR_NO_PAYLOAD,
} = require("../../config/config");

async function sparqlDelete(req, res) {
    // auth
    const authToken = req.headers.authorization;
    //
    let { entry } = req.body;
    entry = decodeId(entry, "srcId");
    entry = decodeId(entry, "dstId");
    //
    // The update data is existing or not.
    if (!entry) {
        return res.status(400).send(ERROR_NO_PAYLOAD);
    }

    // The API has to have the version.
    const { ver } = req.params;
    if (!ver) {
        return res.status(400).send(ERROR_NO_PARAM_VER);
    }

    // The API method is not correct.
    const apiMethod = req.params[0];
    if (!apiMethod) {
        return res.status(400).send(ERROR_API_METHOD);
    }

    // The API method doesn't exist.
    const apiDef = deleteApiList[apiMethod];
    if (!apiDef) {
        return res.status(400).send(ERROR_API_NOT_EXIST);
    }

    // parameters required
    const lackParms = apiDef.required.find((r) => {
        return !entry.hasOwnProperty(r);
    });
    if (lackParms) {
        return res.status(400).send(ERROR_LACK_OF_PARAMS);
    }

    if (!apiDef.hasOwnProperty(ver)) {
        return res.status(400).send(ERROR_API_VER_NOT_EXIST);
    }
    const apiVer = apiDef[ver];

    await verifyToken(authToken)
        .then(async () => {
            await apiVer(entry, (response, error) => {
                if (error) {
                    return res.status(400).send({ error });
                }
                const foundError = response.find((r) => {
                    return r.status !== 200;
                });
                if (foundError) {
                    console.log("status: ", foundError.status);
                    return res
                        .status(foundError.status)
                        .send({ error: foundError.statusText });
                }
                return res.status(200).send({ data: "OK" });
            });
        })
        .catch((error) => {
            // console.log(error);
            res.status(400).send({ error });
        });
}

module.exports = sparqlDelete;
