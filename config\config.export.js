const storeDriver = require("../routers/handleSparql/services/sparql/storeDriver");

const GRAPH = {
    HKLIT: "hklit",
    AUDA_HKLIT: "auda_hklit",
    AUDA: "all_graph",
    GEO: "geo",
    // AUDA_HKLIT: "test", // fixme:測試用
};

const EXTENSION = {
    xlsx: "xlsx",
    zip: "zip",
    tsv: "tsv",
};

let FIRE_BASE_RTDB_ROOT = "/database/local";
if (process.env.MODE === "production") {
    FIRE_BASE_RTDB_ROOT = "/database/production";
} else if (process.env.MODE === "development") {
    FIRE_BASE_RTDB_ROOT = "/database/development";
}

// 向 file server 抓檔案
const FILE_SERVER_BASE_URL =
    process.env.FIE_SERVER_API_DOMAIN || "https://fs-root.daoyidh.com";

// api route
const FILE_SERVER_DOWNLOAD_ROUTE = "database/download/hkbdb";

const exportConfig = {
    GRAPH: GRAPH,
    EXTENSION: EXTENSION,
    FILE_SERVER_BASE_URL: FILE_SERVER_BASE_URL,
    FILE_SERVER_DOWNLOAD_ROUTE: FILE_SERVER_DOWNLOAD_ROUTE,
    pattern: {
        /// 所有資料集的名稱權威檔
        auda: {
            getDatabase: storeDriver.getDbName,
            graph: GRAPH.AUDA,
            sparql: "",
            apiMethod: "export/auda_hklit", // firestore:api/read
            apiVer: "2.1", // firestore:api version
            extension: EXTENSION.xlsx,
            zipExtension: EXTENSION.zip, // 壓縮檔
            dir: "export/auda", // 存放檔案資料夾
        },
        // auda_hklit 資料集
        auda_hklit: {
            getDatabase: storeDriver.getDbName,
            graph: GRAPH.AUDA_HKLIT,
            sparql: "",
            apiMethod: "export/auda_hklit", // firestore:api/read
            apiVer: "2.0", // firestore:api version
            extension: EXTENSION.xlsx,
            zipExtension: EXTENSION.zip, // 壓縮檔
            dir: "export/auda_hklit", // 存放檔案資料夾
        },
        publications: {
            getDatabase: storeDriver.getDbName,
            graph: GRAPH.HKLIT,
            sparql: "",
            apiMethod: "export/hklit",
            apiVer: "2.0",
            extension: EXTENSION.xlsx,
            zipExtension: EXTENSION.zip, // 壓縮檔
            dir: "export/auda_hklit",
        },
        locations_geo: {
            getDatabase: storeDriver.getDbName,
            graph: GRAPH.GEO,
            sparql: "",
            apiMethod: "export/location/geo",
            apiVer: "2.0",
            extension: EXTENSION.xlsx,
            zipExtension: EXTENSION.zip, // 壓縮檔
            dir: "export/locations_geo",
        },
        organizations_geo: {
            getDatabase: storeDriver.getDbName,
            graph: GRAPH.GEO,
            sparql: "",
            apiMethod: "export/organization/geo",
            apiVer: "2.0",
            extension: EXTENSION.xlsx,
            zipExtension: EXTENSION.zip, // 壓縮檔
            dir: "export/organizations_geo",
        },
        // authorNames: {
        //     database: STARDOG_DB,
        //     graph: GRAPH.HKLIT,
        //     sparql: "",
        //     apiMethod: "export/authorNames",
        //     apiVer: "2.0",
        //     extension: EXTENSION.xlsx,
        //     dir: "hkbdb/export/auda_hklit",
        // },
    },
    firebaseRtDb: {
        readWritePath: `${FIRE_BASE_RTDB_ROOT}/export`,
    },
};

module.exports = exportConfig;
