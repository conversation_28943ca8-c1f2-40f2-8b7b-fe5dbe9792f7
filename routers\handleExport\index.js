const express = require("express");
const router = express.Router();
const exportController = require("./exportController");
const { verifyToken } = require("../handleAuth/auth");
const { UNAUTHORIZED } = require("../../commons/httpStatusCode");
const { CustomError } = require("../handleError");

const authHandler = (req, res, next) => {
    const authToken = req.headers.authorization;
    return verifyToken(authToken)
        .then(() => {
            next();
        })
        .catch((error) => {
            console.log(error);
            next(new CustomError(UNAUTHORIZED));
        });
};

router.post("/file", authHandler, exportController.exportFile);

module.exports = router;
