const ERROR_CODE = {
    DEFAULT: {
        name: "DEFAULT",
        code: 500,
        message: "ServerError",
    },
    FIREBASE_READ_ERROR: {
        name: "FIREBASE_READ_ERROR",
        code: 500,
        message: "FIREBASE_READ_ERROR",
    },
    FIREBASE_UPDATE_ERROR: {
        name: "FIREBASE_UPDATE_ERROR",
        code: 500,
        message: "FIREBASE_UPDATE_ERROR",
    },
    PATTERN_NOT_EXIST: {
        name: "PATTERN_NOT_EXIST",
        code: 400,
        message: "尚未支援該匯入類型",
    },
    DB_EXPORT_ERROR: {
        name: "DB_EXPORT_ERROR",
        code: 500,
        message: "Database export failed",
    },
    FILE_CONVERT_ERROR: {
        name: "FILE_CONVERT_ERROR",
        code: 500,
        message: "File converted failed",
    },
    WRITE_FILE_ERROR: {
        name: "WRITE_FILE_ERROR",
        code: 500,
        message: "Write file failed in exportMain",
    },
};

const errorHelper = {
    ERROR_CODE: ERROR_CODE,
    sendErrorCode: (message) => {
        throw new Error(message);
    },
};

module.exports = errorHelper;
