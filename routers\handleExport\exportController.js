const { SERVERERROR } = require("../../commons/httpStatusCode");

//
const exportConfig = require("../../config/config.export");
//
const firebaseHelper = require("./helpers/firebaseHelper");
const fileHelper = require("./helpers/fileHelper");
const errorHelper = require("./helpers/errorHelper");
const commonHelper = require("./helpers/commonHelper");
//
const exportMain = require("./exportMain");
//
const { getFileNameAndKey } = fileHelper;

const exportController = {
    exportFile: async (req, res, next) => {
        try {
            // req.body: {
            //     "user": {
            //         "email": "<EMAIL>",
            //           "name": "abcdefg",
            //           "uid": "asdfqwregafasdfasdfqwrwe"
            //     },
            //     "file": {
            //         "pattern": ["auda_hklit"]
            //     }
            // }
            const { user, file } = req.body;
            console.log(req.body);
            const { pattern } = file || {};
            const patternStr = commonHelper.safeGetFirstEle(pattern);
            const authToken = req.headers.authorization;

            // realtime database specific path
            // rtDbPath: "/database/export";
            const rtDbPath = exportConfig.firebaseRtDb.readWritePath;

            // 先取得 realtime database 設定檔
            const rtDbDataObj = await firebaseHelper.rtDbGet(rtDbPath);
            // console.log(rtDbDataObj);

            // 取得 fileKey and fileName
            const { fileKey, fileName, zipFileName } = getFileNameAndKey(file);
            console.log(
                JSON.stringify({ fileKey, fileName, zipFileName }, null, 2)
            );

            // 產生更新 realtime database 的資料: files 新增 file 資訊, register 新增 file 及 user 資訊
            const updateVal = firebaseHelper.genRtDbSnapValRegister({
                data: rtDbDataObj,
                user,
                fileName: zipFileName,
                fileKey,
                pattern: patternStr,
            });
            // console.log("updateVal", updateVal);
            // 更新 realtime database 的資訊
            const rtUpdateRes = await firebaseHelper.rtDbUpdate(
                rtDbPath,
                updateVal
            );
            // console.log("rtUpdateRes", rtUpdateRes);

            // handleExport: 資料庫匯出, 轉檔, email
            if (!(rtUpdateRes instanceof Error)) {
                // (非同步)匯出流程
                exportMain({
                    user,
                    file,
                    authToken,
                    fileName,
                    zipFileName,
                    fileKey,
                })
                    .then((result) => {
                        console.log("exportMain result", result);
                    })
                    .catch((err) => {
                        console.log("exportMain err", err);
                    });
                res.json({
                    status: "success",
                    message: "File is in exporting.",
                });
            } else {
                res.json(SERVERERROR);
            }
        } catch (err) {
            console.log("error:", err.message);
            let errObj = errorHelper.ERROR_CODE.DEFAULT;
            if (err.message in errorHelper.ERROR_CODE) {
                errObj = errorHelper.ERROR_CODE[err.message];
                console.log("errObj", errObj);
            }
            if (errObj) {
                res.status(errObj.code).json({
                    statusCode: errObj.code,
                    message: errObj.message,
                });
            }
        }
    },
};

module.exports = exportController;
