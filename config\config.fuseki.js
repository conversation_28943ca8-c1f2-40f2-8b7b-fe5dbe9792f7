const configFuseki = {};
configFuseki.port = 5522;
if (process.env.NODE_ENV === "production") {
    const {
        db,
        url,
        user,
        password,
        draftDB,
    } = require("/opt/private-config/hkbdb-fuseki.json");

    configFuseki.db = db;
    configFuseki.url = url;
    configFuseki.user = user;
    configFuseki.password = password;
    configFuseki.draftDB = draftDB;
} else {
    configFuseki.db = "hkbdb2";
    // configFuseki.url = `http://localhost:${configFuseki.port}`;
    configFuseki.url = "http://*************:5522";
    configFuseki.user = "admin";
    configFuseki.password = "admin";
    configFuseki.draftDB = "hkbdb2_draft";
}

console.log("[ENV] FUSEKI_DB", configFuseki.db);
console.log("[ENV] FUSEKI_URL", configFuseki.url);

module.exports = configFuseki;
