const storeDriver = require("../handleHKDrafts/services/sparql/storeDriverDraft");
const suggestMailHelper = {}
let timeConfig = {}

suggestMailHelper.formatTime = (data)=>{
    const timeConfig = {}

    function extractTimeAndDate({ time, date }) {
        const [hour, minute] = time.split(':');
        return { hour, minute, date };
    }

    const { weeklyOption, dailyOption } = data;

    const weeklyConfig = extractTimeAndDate(weeklyOption);
    const dailyConfig = extractTimeAndDate(dailyOption);

    timeConfig.weekly = weeklyConfig;
    timeConfig.daily = dailyConfig;

    return timeConfig
}

suggestMailHelper.getGraph = (limit , offset) => {
    const queryStr = `
        SELECT DISTINCT ?g
        WHERE {
            GRAPH ?g {}
        }
        `
    return storeDriver.makeQuery(queryStr, limit, offset)
        .then((response) => {
            if (response.status !== 200) {
                console.log(response);
            }
            return response;
        })
        .catch((error) => {
            throw error;
        });
}

module.exports = suggestMailHelper;