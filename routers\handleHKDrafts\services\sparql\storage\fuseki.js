const SimpleClient = require("sparql-http-client/SimpleClient");
const { prefixesIri } = require("../rdf");
const {
    DEFAULT_MAX_LIMIT,
    DEFAULT_OFFSET,
} = require("../../../../../config/config");
let client = null;
let dbName = null;

module.exports = {
    getDbName() {
        return dbName;
    },
    setSparql(settings) {
        if (!settings) {
            return;
        }

        const {
            user,
            password,
            url,
            db,
        } = require("../../../../../config/config.fusekiDraft");

        dbName = db;
        client = new SimpleClient({
            user,
            password,
            endpointUrl: `${url}/${dbName}`,
            updateUrl: `${url}/${dbName}/update`,
        });
    },
    //, limit = DEFAULT_MAX_LIMIT, offset = DEFAULT_OFFSET
    async makeQuery(
        queryString,
        limit = DEFAULT_MAX_LIMIT,
        offset = DEFAULT_OFFSET,
        reasoning = false
    ) {
        if (dbName === null) {
            return null;
        }
        const limitOffset =
            limit > 0 && offset >= 0 ? `LIMIT ${limit} OFFSET ${offset}` : "";
        const newQueryString = `${prefixesIri()}\n${queryString}\n${limitOffset}`;

        const response = await client.query.select(newQueryString, {
            headers: {
                accept: "application/sparql-results+json",
            },
        });

        // align with stardog response;
        const resJson = await response.json();
        resJson.body = { results: resJson.results, head: resJson.head };
        return resJson;
    },
    async makeUpdate(queryString) {
        if (dbName === null) {
            return null;
        }
        const newQueryString = `${prefixesIri()}\n${queryString}`;
        return client.query.update(newQueryString);
    },
    // 匯出某個資料庫的某個 graph 及其內的 triple
    exportDbGraph(exportDbName, exportGraph, mineType) {
        // if (!(exportDbName && exportGraph && mineType))
        //     return Promise.resolve(
        //         "lack of exportDbName or exportGraph or mineType"
        //     );
        // if (!myConn) return Promise.resolve(null);
        //
        // // response.body => 不管 mineType 是 application/trig 或 application/json
        // // 皆為 object
        // return db.exportData(
        //     myConn,
        //     dbName || exportDbName,
        //     { mineType: mineType },
        //     {
        //         graphUri: exportGraph,
        //     }
        // );
    },
    // 匯出某個資料庫的某個 graph 下的 triple, 不包含 graph 資訊
    exportGraph(exportDbName, exportGraph, mineType) {
        // if (!(exportDbName && exportGraph && mineType))
        //     return Promise.resolve(
        //         "lack of exportDbName or exportGraph or mineType"
        //     );
        // if (!myConn) return Promise.resolve(null);
        //
        // // response.body => mineType為application/trig => string
        // // response.body => mineType為application/json => object
        // return db.graph.doGet(
        //     myConn,
        //     dbName || exportDbName,
        //     exportGraph,
        //     mineType
        // );
    },
};
